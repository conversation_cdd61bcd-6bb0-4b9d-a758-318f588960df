# Repository Guidelines

## Project Structure & Module Organization
The Next.js App Router lives in `app/`, with API handlers under `app/api/*` and shared layouts in `app/layout.tsx`. UI primitives and feature widgets reside in `components/`, while cross-cutting hooks, auth helpers, and API wrappers are centralized in `lib/`. Shared utility clients, including Supabase helpers, are under `utils/`, and reusable type definitions stay in `types/`. Tests mirror the runtime code inside `__tests__/`, product context is documented in `docs/` and `memory-bank/`, and database migrations live in `supabase/`.

## Build, Test, and Development Commands
- `npm ci` installs the exact dependency tree locked in `package-lock.json`.
- `npm run dev` starts the Next.js dev server at http://localhost:3000 for interactive work.
- `npm run build` compiles a production bundle; follow with `npm start` to serve the optimized output.

## Coding Style & Naming Conventions
Write new code in TypeScript with 2-space indentation. Components use PascalCase filenames in `components/`, hooks are placed in `lib/hooks` with a `use*` prefix, and route segments under `app/` stay lowercase. Favor Tailwind utility classes and MUI components; keep inline styles for last-mile overrides only. Data fetching should go through `lib/services/api.ts` instead of scattered `fetch` calls.

## Security & Configuration Tips
Populate `.env.local` with `NEXT_PUBLIC_SUPABASE_URL`, `NEXT_PUBLIC_SUPABASE_ANON_KEY`, and optional service keys like `API_KEY` or `TEST_USER_ID` for local auth. Keep secrets out of the repo, validate input in API handlers, and prefer server components or API routes for privileged logic.

## Database Workflow
Inspect schema state using the Supabase DB MCP rather than manual queries. Apply structural changes through migration files in `supabase/` and run them with the Supabase CLI to keep environments in sync.

## File Editing
Always use your built in file editing tool to edit files. Don't use python or bash scripts or commands to edit files.