# Bug Fix: Missing Variable Wizard Save Functionality

## Issue Summary
The Missing Variable Wizard had three critical issues:
1. **Save functionality not working**: Values were tracked in memory but never persisted to the database
2. **Confusing UX with multiple save buttons**: Individual "Save Value" buttons per context plus conditional "Save & Close" buttons created confusion and unreliable behavior
3. **Race condition in saveChanges()**: The `hasChanges` memo was stale when `updateVariable()` and `saveChanges()` were called in quick succession

## Root Cause Analysis

### Problem 1: Save Functionality Not Working
The variable save system in this application uses a two-step process:

1. **`updateVariable()`** - Tracks changes in memory using:
   - `variableChanges` Map (stores the new values)
   - `changeTracker` (tracks metadata about changes)

2. **`saveChanges()`** - Actually persists changes to the database via API calls:
   - Calls `api.updateTemplateVariables()` for template-level changes
   - Calls `api.updateCategoryVariables()` for category-level changes
   - Calls `api.updateDatasetVariables()` for dataset-level changes

**What Was Happening:**
The Missing Variable Wizard's individual save buttons were:
1. ✅ Calling `updateVariable()` to track the change in memory
2. ❌ **NOT calling `saveChanges()`** to persist to the database
3. ✅ Marking the context as "saved" in local UI state

This created the illusion of saving without actual database persistence.

### Problem 2: Confusing Multiple Save Buttons
The wizard had three different types of save buttons:
1. **Individual "Save Value" buttons** - One per context/input field
2. **Single-context "Save & Close"** - Shown when `totalContexts === 1`
3. **Multi-context "Save & Close"** - Shown when `totalContexts > 1`

This created confusion:
- Users didn't know which button to click
- Individual save buttons were unreliable (only tracked changes, didn't persist)
- Different buttons appeared based on context count
- Inconsistent behavior between single and multiple contexts

### Problem 3: Race Condition in saveChanges()
**File:** `lib/hooks/useVariableTreeState.ts`

The `saveChanges()` function had a critical race condition:

```typescript
const hasChanges = useMemo(() => {
  return variableChanges.size > 0 || changeTracker.hasChanges();
}, [variableChanges, changeTracker]);

const saveChanges = useCallback(async () => {
  if (!hasChanges) return;  // ❌ Uses stale memo value!
  // ...
}, [hasChanges, ...]);
```

**The Problem:**
1. `hasChanges` is a memoized value that depends on `variableChanges` (React state) and `changeTracker` (object reference)
2. When `updateVariable()` is called, it:
   - Calls `setVariableChanges()` (async React state update)
   - Calls `changeTracker.addChange()` (synchronous, but doesn't change object reference)
3. The `hasChanges` memo doesn't re-compute because:
   - `variableChanges` state hasn't updated yet (React batches state updates)
   - `changeTracker` reference hasn't changed (it's the same object)
4. When `saveChanges()` is called immediately after, it checks the **stale** `hasChanges` value and returns early

**Why it worked in Variable Tree but not in Wizard:**
- **Variable Tree**: User edits → `updateVariable()` → React re-renders → User clicks Save → `saveChanges()` (memo has updated)
- **Wizard**: User clicks Save → `updateVariable()` + `saveChanges()` called immediately → memo hasn't updated yet → save fails

## The Fix

### Changes Made

#### 1. Added `saveChanges` Prop
**File:** `components/template/MissingVariablePanel.tsx`

Added `saveChanges` prop to the interface:
```typescript
interface MissingVariablePanelProps {
  // ... existing props
  updateVariable?: (variableName: string, value: any, variable: VariableWithContext, contextId?: number, contextType?: 'category' | 'dataset', options?: { force?: boolean }) => void;
  saveChanges?: () => Promise<void>;  // NEW
  template_data?: any;
  // ... rest of props
}
```

#### 2. Replaced Individual Save Logic with Global Save
**Removed:**
- Individual "Save Value" buttons (one per context)
- Conditional "Save & Close" button for single contexts
- Conditional "Save & Close" button for multiple contexts
- `savedContexts` state tracking
- Per-context save indicators (checkmarks on tabs)

**Added:**
- Single `handleSaveAllAndClose` function that:
  - Iterates through ALL edited values across all contexts
  - Calls `updateVariable()` for each edited value
  - Calls `saveChanges()` once to persist all changes
  - Clears all edited values and closes the dialog

**Before (old `handleSaveValue` - only saved current context):**
```typescript
const handleSaveValue = useCallback(() => {
  if (!currentContext || !variableDefinition || !updateVariable || !selectedVariable) return;

  const valueToSave = editedValues.get(currentContext.key) ?? currentValue;

  if (currentContext.type === 'template') {
    updateVariable(selectedVariable.variableName, valueToSave, variableDefinition, undefined, undefined, { force: true });
  } else if (currentContext.type === 'category' && currentContext.categoryId !== undefined) {
    updateVariable(selectedVariable.variableName, valueToSave, variableDefinition, currentContext.categoryId, 'category', { force: true });
  } else if (currentContext.type === 'dataset' && currentContext.datasetId !== undefined) {
    updateVariable(selectedVariable.variableName, valueToSave, variableDefinition, currentContext.datasetId, 'dataset', { force: true });
  }

  // Mark this context as saved
  setSavedContexts(prev => {
    const next = new Set(prev);
    next.add(currentContext.key);
    return next;
  });

  // Clear edited value for this context
  setEditedValues(prev => {
    const next = new Map(prev);
    next.delete(currentContext.key);
    return next;
  });
}, [currentContext, variableDefinition, updateVariable, selectedVariable, editedValues, currentValue]);
```

**After (new `handleSaveAllAndClose` - saves ALL contexts):**
```typescript
const handleSaveAllAndClose = useCallback(async () => {
  if (!variableDefinition || !updateVariable || !selectedVariable) return;

  // Update all edited values to the change tracker
  Array.from(editedValues.entries()).forEach(([contextKey, value]) => {
    // Find the context info from the key
    const context = contextsForLevel.find(ctx => ctx.key === contextKey);
    if (!context) return;

    if (context.type === 'template') {
      updateVariable(selectedVariable.variableName, value, variableDefinition, undefined, undefined, { force: true });
    } else if (context.type === 'category' && context.categoryId !== undefined) {
      updateVariable(selectedVariable.variableName, value, variableDefinition, context.categoryId, 'category', { force: true });
    } else if (context.type === 'dataset' && context.datasetId !== undefined) {
      updateVariable(selectedVariable.variableName, value, variableDefinition, context.datasetId, 'dataset', { force: true });
    }
  });

  // Actually save all changes to the database
  if (saveChanges) {
    try {
      await saveChanges();
    } catch (error) {
      console.error('Failed to save variables:', error);
      // Don't close if save failed
      return;
    }
  }

  // Clear all edited values and close
  setEditedValues(new Map());

  setTimeout(() => {
    onClose();
  }, 100);
}, [variableDefinition, updateVariable, selectedVariable, editedValues, contextsForLevel, saveChanges, onClose]);
```

#### 3. Simplified UI - Single Global Save Button
**Removed from inline editor section (lines 651-691):**
```typescript
{/* Save Actions */}
<Box sx={{ display: 'flex', gap: 1, mt: 2, justifyContent: 'flex-end' }}>
  <Tooltip title={savedContexts.has(currentContext.key) ? 'Already saved' : ''}>
    <span>
      <Button
        variant="contained"
        size="small"
        startIcon={<SaveIcon />}
        onClick={handleSaveValue}
        disabled={savedContexts.has(currentContext.key) && !hasUnsavedChanges}
      >
        Save Value
      </Button>
    </span>
  </Tooltip>
  {totalContexts === 1 && (
    <Button
      variant="outlined"
      size="small"
      onClick={handleSaveAndClose}
    >
      Save & Close
    </Button>
  )}
</Box>
```

**Updated dialog footer (lines 673-682):**
```typescript
{shouldShowInlineEditor && (
  <Button
    variant="contained"
    color="primary"
    onClick={handleSaveAllAndClose}
    disabled={!hasUnsavedChanges}
  >
    Save & Close
  </Button>
)}
```

This button is now:
- Always visible when the inline editor is shown
- Works for both single and multiple contexts
- Only enabled when there are unsaved changes
- Saves ALL pending changes across all contexts

#### 4. Fixed Race Condition in saveChanges()
**File:** `lib/hooks/useVariableTreeState.ts`

Changed `saveChanges()` to check `changeTracker.hasChanges()` directly instead of using the memoized `hasChanges` value:

**Before:**
```typescript
const saveChanges = useCallback(async () => {
  if (!hasChanges) return;  // ❌ Stale memo value
  // ...
}, [hasChanges, changeTracker, ...]);
```

**After:**
```typescript
const saveChanges = useCallback(async () => {
  // Check directly from changeTracker to avoid stale memo issues
  // when updateVariable and saveChanges are called in quick succession
  if (!changeTracker.hasChanges()) return;  // ✅ Always current
  // ...
}, [changeTracker, ...]);  // Removed hasChanges from dependencies
```

This ensures that `saveChanges()` always checks the current state of the `changeTracker`, even when called immediately after `updateVariable()`.

#### 5. Updated Parent Component
**File:** `components/template/VariableTreeView.tsx`

Passed `saveChanges` prop to `MissingVariablePanel`:
```typescript
<MissingVariablePanel
  // ... existing props
  updateVariable={updateVariable}
  saveChanges={saveChanges}  // NEW
  template_data={data}
  // ... rest of props
/>
```

#### 6. Cleaned Up Unused Code
- Removed `savedContexts` state variable
- Removed `setSavedContexts` calls
- Removed `savedCount` variable
- Removed checkmark indicators from tabs
- Removed "X of Y configured" display
- Removed unused imports (`Tooltip`, `SaveIcon`, `CheckCircleIcon`)

## Testing Recommendations

1. **Single Context Save Test:**
   - Open Missing Variable Wizard for a variable with one context
   - Edit the variable value
   - Verify only ONE "Save & Close" button is visible in the dialog footer
   - Click "Save & Close"
   - Verify the value is saved to the database (check via database query or reload the page)
   - Verify the dialog closes after successful save

2. **Multi-Context Save Test:**
   - Open Missing Variable Wizard for a variable with multiple contexts (e.g., multiple categories or datasets)
   - Edit values in 2-3 different contexts using the tabs/dropdown
   - Verify NO individual "Save Value" buttons appear
   - Verify only ONE "Save & Close" button is visible in the dialog footer
   - Click "Save & Close"
   - Verify ALL edited values are persisted to the database
   - Verify the dialog closes after successful save

3. **Partial Edit Test:**
   - Open wizard with multiple contexts
   - Edit only some contexts (not all)
   - Click "Save & Close"
   - Verify only the edited values are saved
   - Verify unedited contexts remain unchanged

4. **Error Handling Test:**
   - Simulate a network error or API failure
   - Edit a variable and click "Save & Close"
   - Verify the dialog does NOT close on error
   - Verify error is logged to console
   - Verify the user can retry the save

5. **Button State Test:**
   - Open wizard and verify "Save & Close" is disabled (no changes yet)
   - Edit a value
   - Verify "Save & Close" becomes enabled
   - Click "Save & Close"
   - Verify button is disabled again after successful save

6. **Visual Indicator Test:**
   - Open wizard with multiple contexts
   - Edit a value in one context
   - Switch to another context tab
   - Verify the edited context shows a small orange dot indicator
   - Verify no checkmarks appear (removed feature)
   - Verify no "X of Y configured" text appears (removed feature)

## Impact
- ✅ Variables saved in the Missing Variable Wizard now persist to the database
- ✅ Proper error handling prevents dialog from closing on save failure
- ✅ Consistent save behavior between Variable Tree View and Missing Variable Wizard
- ✅ Simplified UX with single, clear save action
- ✅ All pending changes across all contexts saved in one operation
- ✅ Reduced confusion from multiple save buttons
- ✅ No breaking changes to existing functionality

## User Experience Improvements
**Before:**
- 3 different types of save buttons (confusing)
- Individual saves were unreliable (didn't persist)
- Different UI based on context count
- Users unsure which button to click

**After:**
- Single "Save & Close" button (clear and simple)
- Always saves ALL pending changes
- Consistent UI regardless of context count
- Clear indication of unsaved changes (button disabled when no changes)

## Files Modified
1. `components/template/MissingVariablePanel.tsx` - Major refactoring of save UI and logic
2. `components/template/VariableTreeView.tsx` - Added `saveChanges` prop
3. `lib/hooks/useVariableTreeState.ts` - Fixed race condition in `saveChanges()`

