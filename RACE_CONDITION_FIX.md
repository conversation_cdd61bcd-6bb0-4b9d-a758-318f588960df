# Race Condition Fix: saveChanges() with Stale Memo

## The Bug

The `saveChanges()` function in `lib/hooks/useVariableTreeState.ts` had a critical race condition that prevented saves when `updateVariable()` and `saveChanges()` were called in quick succession.

## Root Cause Analysis

### The Code Structure

```typescript
// Memoized value based on React state and object reference
const hasChanges = useMemo(() => {
  return variableChanges.size > 0 || changeTracker.hasChanges();
}, [variableChanges, changeTracker]);

// Save function that checks the memo
const saveChanges = useCallback(async () => {
  if (!hasChanges) return;  // ❌ PROBLEM: Uses stale memo
  
  const changes = changeTracker.getChanges();
  // ... save logic
}, [hasChanges, changeTracker, ...]);
```

### The Problem

1. **`hasChanges` is a memoized value** that depends on:
   - `variableChanges` - React state (Map)
   - `changeTracker` - Object reference

2. **When `updateVariable()` is called**, it:
   ```typescript
   setVariableChanges(prev => {
     const next = new Map(prev);
     next.set(key, newValue);
     return next;
   });
   
   changeTracker.addChange(...);  // Modifies internal state, not reference
   ```

3. **The memo doesn't re-compute** because:
   - `variableChanges` state update is **asynchronous** (React batches updates)
   - `changeTracker` **reference doesn't change** (same object, different internal state)

4. **When `saveChanges()` is called immediately after**, it:
   - Checks `hasChanges` which is still the **old memoized value** (false)
   - Returns early without saving
   - The actual changes ARE in `changeTracker`, but the memo hasn't updated yet

### Why It Worked in Variable Tree

**Variable Tree Flow:**
```
User edits input
  ↓
onChange → updateVariable()
  ↓
React re-renders (state updates)
  ↓
hasChanges memo re-computes (now true)
  ↓
User clicks "Save" button
  ↓
saveChanges() → checks hasChanges (true) → saves ✅
```

**Time gap between edit and save allows React to update the memo.**

### Why It Failed in Missing Variable Wizard

**Wizard Flow:**
```
User clicks "Save & Close"
  ↓
handleSaveAllAndClose() {
  updateVariable()  // Queues state update, modifies changeTracker
  saveChanges()     // Checks hasChanges (still false!) → returns early ❌
}
```

**No time gap - both calls happen synchronously in the same function.**

## The Fix

Changed `saveChanges()` to check `changeTracker.hasChanges()` **directly** instead of using the memoized value:

### Before (Broken)
```typescript
const saveChanges = useCallback(async () => {
  if (!hasChanges) return;  // ❌ Stale memo value
  
  const changes = changeTracker.getChanges();
  // ...
}, [hasChanges, changeTracker, ...]);
```

### After (Fixed)
```typescript
const saveChanges = useCallback(async () => {
  // Check directly from changeTracker to avoid stale memo issues
  // when updateVariable and saveChanges are called in quick succession
  if (!changeTracker.hasChanges()) return;  // ✅ Always current
  
  const changes = changeTracker.getChanges();
  // ...
}, [changeTracker, ...]);  // Removed hasChanges from dependencies
```

## Why This Works

1. **`changeTracker.hasChanges()` is synchronous** - it directly checks the internal Map
2. **No dependency on React state** - doesn't wait for state updates
3. **Always current** - reflects changes immediately after `updateVariable()` calls `changeTracker.addChange()`

## Impact

- ✅ Fixes save functionality in Missing Variable Wizard
- ✅ No impact on Variable Tree (still works as before)
- ✅ More robust - works regardless of timing between `updateVariable()` and `saveChanges()`
- ✅ No breaking changes

## Testing

### Test Case 1: Quick Succession (Wizard Pattern)
```typescript
updateVariable('var1', 'value1', variable);
updateVariable('var2', 'value2', variable);
await saveChanges();  // Should save both changes ✅
```

### Test Case 2: With Delay (Variable Tree Pattern)
```typescript
updateVariable('var1', 'value1', variable);
// ... user does other things ...
await saveChanges();  // Should still work ✅
```

### Test Case 3: No Changes
```typescript
await saveChanges();  // Should return early (no changes) ✅
```

## Lessons Learned

1. **Be careful with memoized values** that depend on object references
2. **React state updates are asynchronous** - don't assume immediate availability
3. **When in doubt, check the source directly** instead of relying on derived/memoized values
4. **Test both synchronous and asynchronous usage patterns**

## Related Files

- `lib/hooks/useVariableTreeState.ts` - The fix
- `lib/utils/changeTracking.ts` - VariableChangeTracker implementation
- `components/template/MissingVariablePanel.tsx` - Where the bug manifested

