# Missing Variables - Inline Editing Enhancement Plan

## Executive Summary

This document outlines the design and implementation plan for adding inline variable editing capabilities to the Missing Variables modal, allowing users to set variable values directly within the wizard without navigating away.

---

## UX Design Solution

### Core Concept
**Progressive Disclosure**: Show editing UI only when user selects a hierarchy level, keeping the initial view clean and focused.

### User Flow

```
1. User opens Missing Variables modal
   ↓
2. User sees variable info + hierarchy level options
   ↓
3. User selects a hierarchy level (Template/Category/Dataset)
   ↓
4. Inline editor appears below the level selection
   ↓
5. If multiple contexts at that level:
   - Show context switcher (tabs/segmented control)
   - User can switch between contexts
   ↓
6. User edits value(s)
   ↓
7. User clicks "Save" or "Save & Close"
   ↓
8. Values are saved, modal shows success or closes
```

---

## Design Decisions

### 1. When to Show Inline Editor
**Decision**: Show editor immediately when user selects a hierarchy level

**Rationale**:
- Clear cause-and-effect relationship
- No additional click required
- Editor appears in context of the selection

### 2. Handling Multiple Contexts

#### Scenario A: Template Level (1 context)
- Show single input component
- No switcher needed
- Simple, clean UI

#### Scenario B: Category Level (1-N categories)
- If 1 category: Show single input
- If 2-5 categories: Show tabs for switching
- If 6+ categories: Show dropdown selector + prev/next buttons

#### Scenario C: Dataset Level (1-N datasets)
- If 1 dataset: Show single input
- If 2-10 datasets: Show tabs for switching
- If 11+ datasets: Show dropdown selector + prev/next buttons
- Show progress indicator: "Dataset 3 of 15"

### 3. Context Switcher UI Patterns

**For 2-5 contexts**: Material-UI Tabs
```
[Dataset 1] [Dataset 2] [Dataset 3]
```

**For 6+ contexts**: Compact selector
```
[<] Dataset 3 of 15 [>]  [Dropdown ▼]
```

### 4. Save Behavior

**Three action buttons**:

1. **"Save Value"** (Primary)
   - Saves current context only
   - Stays in modal
   - Allows batch editing of multiple contexts
   - Shows checkmark on saved contexts

2. **"Save & Close"** (Secondary)
   - Saves current context
   - Closes modal
   - Quick action for single edits

3. **"Navigate to Level"** (Tertiary/Link)
   - Old behavior
   - For power users who prefer full tree view
   - Opens in new state, doesn't save

### 5. Visual Feedback

**Saved Contexts**:
- Show checkmark icon next to saved context tabs
- Green highlight on saved contexts
- Progress indicator: "2 of 5 datasets configured"

**Unsaved Changes**:
- Orange dot on tab with unsaved changes
- Warning when switching contexts with unsaved changes
- Confirmation dialog when closing with unsaved changes

---

## Technical Implementation

### Phase 1: Basic Inline Editor (Single Context)

**Goal**: Show input component for template level (simplest case)

**Changes**:
1. Add state for inline editing mode
2. Render VariableInputRenderer when level selected
3. Wire up onChange handler
4. Implement save functionality
5. Add "Save Value" and "Save & Close" buttons

**Files**:
- `components/template/MissingVariablePanel.tsx`

### Phase 2: Context Switching (Multiple Contexts)

**Goal**: Handle multiple datasets/categories at selected level

**Changes**:
1. Detect number of contexts at selected level
2. Render appropriate switcher UI (tabs vs dropdown)
3. Track which context is currently selected
4. Update input component when context switches
5. Maintain separate values for each context

**New Components**:
- `ContextSwitcher.tsx` (optional, or inline in MissingVariablePanel)

### Phase 3: Save State Tracking

**Goal**: Track which contexts have been saved

**Changes**:
1. Add state to track saved contexts
2. Show checkmarks on saved contexts
3. Show progress indicator
4. Handle unsaved changes warnings

### Phase 4: Polish & Edge Cases

**Goal**: Handle all edge cases and improve UX

**Changes**:
1. Validation before save
2. Error handling
3. Loading states
4. Accessibility improvements
5. Keyboard navigation

---

## Data Structures

### Context Identifier
```typescript
interface ContextIdentifier {
  type: 'template' | 'category' | 'dataset';
  id?: number; // undefined for template, categoryId or datasetId otherwise
  label: string; // Display name
}
```

### Edited Value Tracking
```typescript
// Map of contextKey -> value
// contextKey format: "template" | "category-{id}" | "dataset-{id}"
const editedValues = new Map<string, any>();
```

### Saved Context Tracking
```typescript
// Set of contextKeys that have been saved
const savedContexts = new Set<string>();
```

---

## UI Layout

### Before Level Selection
```
┌─────────────────────────────────────────┐
│ Missing Variables                       │
├─────────────────────────────────────────┤
│ Variable: LEVERARM_X                    │
│ Reason: Required variable not defined   │
│ Constraints: Global, Category           │
│                                         │
│ Affected Datasets (3)                   │
│ [Dataset1] [Dataset2] [Dataset3]        │
│                                         │
│ Where to Define This Variable           │
│ ○ Global (Template) [Recommended]       │
│ ○ Category: Europe                      │
│                                         │
│ [Close] [Navigate to Level]             │
└─────────────────────────────────────────┘
```

### After Level Selection (Single Context)
```
┌─────────────────────────────────────────┐
│ Missing Variables                       │
├─────────────────────────────────────────┤
│ Variable: LEVERARM_X                    │
│ ...                                     │
│ Where to Define This Variable           │
│ ● Global (Template) [Recommended]       │
│ ○ Category: Europe                      │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ Set Value at Global (Template)      │ │
│ ├─────────────────────────────────────┤ │
│ │ [Input Component Here]              │ │
│ │ LEVERARM_X: [___0.5___] meters      │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ [Close] [Navigate] [Save] [Save&Close] │
└─────────────────────────────────────────┘
```

### After Level Selection (Multiple Contexts - Tabs)
```
┌─────────────────────────────────────────┐
│ Missing Variables                       │
├─────────────────────────────────────────┤
│ Variable: LEVERARM_X                    │
│ ...                                     │
│ Where to Define This Variable           │
│ ○ Global (Template)                     │
│ ● Dataset Level                         │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ Set Value at Dataset Level          │ │
│ ├─────────────────────────────────────┤ │
│ │ [Dataset1✓] [Dataset2] [Dataset3]   │ │
│ │                                     │ │
│ │ Dataset: Dataset2                   │ │
│ │ LEVERARM_X: [___0.5___] meters      │ │
│ │                                     │ │
│ │ Progress: 1 of 3 datasets configured│ │
│ └─────────────────────────────────────┘ │
│                                         │
│ [Close] [Navigate] [Save] [Save&Close] │
└─────────────────────────────────────────┘
```

### After Level Selection (Many Contexts - Compact)
```
┌─────────────────────────────────────────┐
│ Missing Variables                       │
├─────────────────────────────────────────┤
│ Variable: LEVERARM_X                    │
│ ...                                     │
│ Where to Define This Variable           │
│ ○ Global (Template)                     │
│ ● Dataset Level                         │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ Set Value at Dataset Level          │ │
│ ├─────────────────────────────────────┤ │
│ │ [<] Dataset 3 of 15 [>] [Select ▼] │ │
│ │                                     │ │
│ │ Dataset: Dataset003                 │ │
│ │ LEVERARM_X: [___0.5___] meters      │ │
│ │                                     │ │
│ │ Progress: 2 of 15 configured ✓      │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ [Close] [Navigate] [Save] [Save&Close] │
└─────────────────────────────────────────┘
```

---

## Implementation Checklist

### Phase 1: Basic Inline Editor
- [ ] Add state for inline editing mode
- [ ] Add state for edited values
- [ ] Detect when level is selected
- [ ] Get variable definition from getVariablesByName
- [ ] Render VariableInputRenderer component
- [ ] Wire up onChange handler
- [ ] Implement save functionality
- [ ] Add "Save Value" button
- [ ] Add "Save & Close" button
- [ ] Test with template level (single context)

### Phase 2: Context Switching
- [ ] Calculate contexts for selected level
- [ ] Add state for selected context index
- [ ] Render tabs for 2-10 contexts
- [ ] Render compact selector for 11+ contexts
- [ ] Handle context switching
- [ ] Update input component on context change
- [ ] Maintain separate values per context
- [ ] Test with multiple datasets

### Phase 3: Save State Tracking
- [ ] Add state for saved contexts
- [ ] Show checkmarks on saved tabs
- [ ] Show progress indicator
- [ ] Warn on unsaved changes when switching
- [ ] Warn on unsaved changes when closing
- [ ] Test save state persistence

### Phase 4: Polish
- [ ] Add loading states
- [ ] Add error handling
- [ ] Add validation
- [ ] Improve accessibility
- [ ] Add keyboard shortcuts
- [ ] Add animations/transitions
- [ ] Test all edge cases

---

## Edge Cases to Handle

1. **Variable not found**: Show error message
2. **No getVariablesByName prop**: Disable inline editing, show only navigate button
3. **No updateVariable prop**: Disable save, show only navigate button
4. **Validation errors**: Show error message, prevent save
5. **Save fails**: Show error, keep modal open
6. **User switches variable**: Reset inline editor state
7. **User switches level**: Reset context selection, keep edited values
8. **Rapid clicking**: Debounce save button
9. **Large number of contexts (100+)**: Use virtual scrolling in dropdown

---

## Future Enhancements

1. **Bulk Edit Mode**: Set same value for all contexts at once
2. **Copy Value**: Copy value from one context to others
3. **Smart Defaults**: Suggest values based on similar variables
4. **Validation Preview**: Show which datasets will be affected before saving
5. **Undo/Redo**: Allow undoing changes before closing modal
6. **Keyboard Shortcuts**: Ctrl+S to save, Ctrl+Enter to save & close
7. **Auto-save**: Optionally auto-save on value change

---

## Success Metrics

1. **Reduced Navigation**: Users complete variable setup without leaving modal
2. **Faster Workflow**: Time to resolve missing variables decreases
3. **Fewer Errors**: Validation happens before save
4. **User Satisfaction**: Positive feedback on inline editing feature

---

## Rollout Plan

1. **Phase 1**: Release basic inline editor (template level only)
2. **Gather Feedback**: Monitor usage, collect user feedback
3. **Phase 2**: Add context switching for multiple datasets
4. **Phase 3**: Add save state tracking and progress indicators
5. **Phase 4**: Polish and edge case handling
6. **Full Release**: Enable for all users

---

This plan provides a clear roadmap for implementing inline editing in the Missing Variables modal while maintaining a clean, intuitive user experience.

