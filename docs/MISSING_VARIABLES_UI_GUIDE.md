# Missing Variables Panel - UI/UX Guide

## Visual Comparison: Before vs After

### Before: Old Implementation

```
┌─────────────────────────────────────────────┐
│ Missing Variable                        [X] │
├─────────────────────────────────────────────┤
│ Context: Category: Europe                  │
│                                             │
│ Select variable:                            │
│ ┌─────────────────────────────────────────┐ │
│ │ LEVERARM_X – Dataset1              ▼   │ │
│ └─────────────────────────────────────────┘ │
│   • LEVERARM_X – Dataset1                   │
│   • LEVERARM_X – Dataset2                   │
│   • LEVERARM_X – Dataset3                   │
│   • LEVERARM_Y – Dataset1                   │
│   • LEVERARM_Y – Dataset2                   │
│                                             │
│ LEVERARM_X                                  │
│ Dataset: Dataset1                           │
│                                             │
│ Cause: Required variable is not defined    │
│ Configurable at levels: Global, Category   │
│                                             │
│ [Global] [Category: Europe]                 │
│                                             │
│ Affected: 3/10 datasets                     │
│                                             │
├─────────────────────────────────────────────┤
│        [Close] [Bulk fix] [Define once]    │
│                           [Define here]     │
└─────────────────────────────────────────────┘
```

**Problems:**
- ❌ Dropdown cluttered with dataset-variable combinations
- ❌ Can't see all affected datasets at once
- ❌ No clear indication of optimal level
- ❌ Buttons don't navigate to location
- ❌ Hard to understand hierarchy impact

---

### After: New Implementation

```
┌──────────────────────────────────────────────────────────┐
│ Missing Variables                                    [X] │
├──────────────────────────────────────────────────────────┤
│ Context: Category: Europe                               │
│                                                          │
│ Select variable to configure:                           │
│ ┌────────────────────────────────────────────────────┐  │
│ │ LEVERARM_X (3 datasets)                       ▼   │  │
│ └────────────────────────────────────────────────────┘  │
│   • LEVERARM_X (3 datasets)                             │
│   • LEVERARM_Y (2 datasets)                             │
│                                                          │
│ ┌────────────────────────────────────────────────────┐  │
│ │ LEVERARM_X                                         │  │
│ │ Required variable is not defined anywhere.         │  │
│ │ Configurable at: Global, Category: Europe          │  │
│ └────────────────────────────────────────────────────┘  │
│                                                          │
│ Affected Datasets (3)                                   │
│ ┌──────────┐ ┌──────────┐ ┌──────────┐                 │
│ │ Dataset1 │ │ Dataset2 │ │ Dataset3 │                 │
│ └──────────┘ └──────────┘ └──────────┘                 │
│ Click a dataset to navigate to it in the tree          │
│                                                          │
│ Where to Define This Variable                           │
│ Choose the hierarchy level where you want to set this   │
│ variable. Higher levels affect more datasets.           │
│                                                          │
│ ┌────────────────────────────────────────────────────┐  │
│ │ ○ Global (Template) [Recommended]                  │  │
│ │   Will resolve 3 of 3 affected datasets            │  │
│ └────────────────────────────────────────────────────┘  │
│                                                          │
│ ┌────────────────────────────────────────────────────┐  │
│ │ ○ Category: Europe                                 │  │
│ │   Will resolve 3 of 3 affected datasets            │  │
│ └────────────────────────────────────────────────────┘  │
│                                                          │
│ ┌────────────────────────────────────────────────────┐  │
│ │ ○ Category: Europe/Germany                         │  │
│ │   Will resolve 2 of 3 affected datasets            │  │
│ └────────────────────────────────────────────────────┘  │
│                                                          │
├──────────────────────────────────────────────────────────┤
│                    [Close] [Define at Global (Template)] │
└──────────────────────────────────────────────────────────┘
```

**Improvements:**
- ✅ Clean dropdown with unique variable names
- ✅ All affected datasets visible as clickable chips
- ✅ Clear hierarchy with impact preview
- ✅ Recommended level highlighted
- ✅ Functional navigation button
- ✅ Easy to understand and use

---

## User Workflows

### Workflow 1: Resolving a Missing Variable

**Step 1: Identify Missing Variables**
- Red exclamation mark (!) appears on categories/datasets with missing variables
- Tooltip shows: "3/10 datasets missing required values"

**Step 2: Open Missing Variables Panel**
- Click the red exclamation mark
- Panel opens showing all missing variables in that context

**Step 3: Select Variable**
- If multiple variables are missing, use dropdown to select one
- Dropdown shows: "LEVERARM_X (3 datasets)"

**Step 4: Review Impact**
- See all affected datasets as clickable chips
- Review hierarchy options with impact counts
- Recommended level is highlighted

**Step 5: Define Variable**
- Click "Define at [Level]" button
- Automatically navigates to that level in tree
- Variable is highlighted and ready to edit
- Enter the value and save

### Workflow 2: Navigating to a Specific Dataset

**Step 1: Open Missing Variables Panel**
- Click red exclamation mark on any category/dataset

**Step 2: Select Variable**
- Choose the variable you want to configure

**Step 3: Navigate to Dataset**
- Click on any dataset chip in "Affected Datasets" section
- Tree automatically expands to show that dataset
- Dataset is selected
- Variable is highlighted

**Step 4: Configure**
- Edit the variable value for that specific dataset
- Save changes

### Workflow 3: Understanding Hierarchy Impact

**Step 1: Open Panel**
- Click red exclamation mark

**Step 2: Review Levels**
- See all allowed levels in "Where to Define This Variable" section
- Each level shows impact: "Will resolve X of Y affected datasets"

**Step 3: Make Informed Decision**
- Template level: Affects all datasets (most efficient)
- Category level: Affects datasets in that category
- Dataset level: Affects only that specific dataset

**Step 4: Choose Optimal Level**
- Use recommended level (highlighted)
- Or select a different level based on your needs

---

## UI Components Explained

### 1. Variable Selector Dropdown
```
┌────────────────────────────────────────┐
│ LEVERARM_X (3 datasets)           ▼   │
└────────────────────────────────────────┘
```
- Shows unique variable names only
- Dataset count in parentheses
- Only appears when multiple variables are missing

### 2. Variable Information Box
```
┌────────────────────────────────────────┐
│ LEVERARM_X                             │
│ Required variable is not defined...    │
│ Configurable at: Global, Category      │
└────────────────────────────────────────┘
```
- Variable name as heading
- Reason why it's missing
- Allowed levels (from constraints)

### 3. Affected Datasets Section
```
Affected Datasets (3)
┌──────────┐ ┌──────────┐ ┌──────────┐
│ Dataset1 │ │ Dataset2 │ │ Dataset3 │
└──────────┘ └──────────┘ └──────────┘
Click a dataset to navigate to it in the tree
```
- Clickable chips for each dataset
- Hover effect shows they're interactive
- Click navigates to that dataset in tree

### 4. Hierarchy Options (Radio Cards)
```
┌────────────────────────────────────────┐
│ ○ Global (Template) [Recommended]      │
│   Will resolve 3 of 3 affected datasets│
└────────────────────────────────────────┘
```
- Radio button for selection
- Level name clearly displayed
- "Recommended" badge on optimal level
- Impact preview shows how many datasets affected
- Highlighted border for recommended option

### 5. Action Button
```
[Define at Global (Template)]
```
- Dynamic label based on selection
- Disabled if no valid level available
- Primary action (blue, prominent)
- Closes modal and navigates automatically

---

## Design Principles

### 1. Progressive Disclosure
- Show most important information first
- Details revealed as user interacts
- Dropdown only appears when needed (multiple variables)

### 2. Clear Hierarchy
- Visual cards for each level option
- Impact counts help users understand scope
- Recommended option clearly marked

### 3. Direct Manipulation
- Clickable dataset chips
- Radio buttons for level selection
- Immediate feedback on hover

### 4. Contextual Help
- Tooltips explain constraints
- Caption text guides user actions
- Error messages are clear and actionable

### 5. Efficient Workflow
- Minimal clicks to resolve issues
- Automatic navigation after action
- Smart defaults (recommended level)

---

## Accessibility Features

### Keyboard Navigation
- Tab through all interactive elements
- Enter/Space to activate buttons and chips
- Arrow keys in radio group

### Screen Reader Support
- Proper ARIA labels on all controls
- Descriptive button text
- Clear heading hierarchy

### Visual Indicators
- High contrast for important elements
- Color not the only indicator (icons, text)
- Clear focus states

---

## Responsive Behavior

### Desktop (md and up)
- Full width dialog (maxWidth: 'md')
- Side-by-side layout for chips
- All information visible at once

### Mobile/Tablet
- Stacked layout
- Chips wrap to multiple rows
- Scrollable content area
- Touch-friendly tap targets

---

## Error States

### Schema Configuration Error
```
┌────────────────────────────────────────┐
│ ⚠ Schema configuration issue:          │
│ This required variable cannot be set   │
│ due to min/max level constraints.      │
│ Please review the template config.     │
└────────────────────────────────────────┘
```
- Red alert box
- Clear error message
- Guidance on how to fix
- Action button disabled

### No Allowed Levels
- Shows constraint information
- Explains why variable can't be set
- Suggests reviewing schema

---

## Performance Considerations

### Efficient Grouping
- Variables grouped once on mount
- Memoized calculations for impact
- No unnecessary re-renders

### Lazy Navigation
- Tree expansion only when needed
- Scroll position preserved
- Smooth animations

### Data Optimization
- Reuses existing data structures
- No additional API calls
- Client-side processing only

