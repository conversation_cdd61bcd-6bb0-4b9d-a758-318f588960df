# Missing Variables - Developer Guide

## Quick Reference

### Key Files
- `components/template/MissingVariablePanel.tsx` - Main modal component
- `components/template/VariableTreeView.tsx` - Parent component with navigation logic
- `types/variable.ts` - Type definitions

### New Functions in VariableTreeView

#### `navigateToDataset(datasetId: number, variableName?: string)`
Navigates to a specific dataset in the tree and optionally highlights a variable.

**Parameters:**
- `datasetId` - The ID of the dataset to navigate to
- `variableName` - Optional variable name to highlight after navigation

**Usage:**
```typescript
navigateToDataset(123, 'LEVERARM_X');
```

**Behavior:**
1. Finds dataset in tree structure
2. Expands all parent categories
3. Selects the dataset node
4. Restores scroll position for that level
5. If variableName provided, scrolls to and highlights the variable

---

#### `defineAtLevel(variableName: string, level: VariableLevelOption)`
Sets a variable at a specific hierarchy level and navigates to that location.

**Parameters:**
- `variableName` - Name of the variable to define
- `level` - The hierarchy level option (contains nodeType, nodeId, etc.)

**Usage:**
```typescript
defineAtLevel('LEVERARM_X', {
  level: 0,
  label: 'Global (Template)',
  nodeKey: 'global-root',
  nodeType: 'template'
});
```

**Behavior:**
1. Gets variable definition
2. Resolves default value from schema
3. Updates variable at specified level
4. Navigates to that level in tree
5. Highlights the variable
6. Focuses the variable for editing

---

## Data Flow

### Opening the Missing Variables Panel

```typescript
// User clicks red exclamation mark on tree node
handleTreeNodeMissingClick(treeItem)
  ↓
// Extract missing details from tree item
const detailOptions = treeItem.missingSummary.details.filter(d => !d.schemaError)
  ↓
// Open panel with details
openMissingPanel(variableName, state, variable, nodeId, datasetId, {
  detailOptions,
  summary: treeItem.missingSummary,
  originLabel: treeItem.label
})
  ↓
// Panel renders with grouped variables
<MissingVariablePanel
  details={detailOptions}
  onNavigateToDataset={navigateToDataset}
  onDefineAtLevel={defineAtLevel}
/>
```

### Grouping Variables

```typescript
// In MissingVariablePanel component
const groupedVariables = useMemo(() => {
  const groups = new Map<string, GroupedVariableInfo>();
  
  detailOptions.forEach(detail => {
    if (!groups.has(detail.variableName)) {
      // Create new group for this variable
      groups.set(detail.variableName, {
        variableName: detail.variableName,
        affectedDatasets: [],
        allowedLevels: detail.allowedLevels,
        // ... other properties
      });
    }
    
    // Add dataset to group
    const group = groups.get(detail.variableName)!;
    group.affectedDatasets.push({
      datasetId: detail.datasetId,
      datasetName: detail.datasetName,
      detail
    });
  });
  
  return Array.from(groups.values());
}, [detailOptions]);
```

### Calculating Level Impact

```typescript
const levelImpact = useMemo(() => {
  const impact = new Map<string, number>();
  
  selectedVariable.allowedLevels.forEach(level => {
    let count = 0;
    
    if (level.nodeType === 'template') {
      // Template affects all datasets
      count = selectedVariable.affectedDatasets.length;
    } else if (level.nodeType === 'category') {
      // Category affects datasets that have this level in allowedLevels
      count = selectedVariable.affectedDatasets.filter(ds =>
        ds.detail.allowedLevels?.some(al => al.nodeKey === level.nodeKey)
      ).length;
    } else if (level.nodeType === 'dataset') {
      // Dataset level affects only that specific dataset
      count = selectedVariable.affectedDatasets.filter(ds =>
        ds.datasetId === level.datasetId
      ).length;
    }
    
    impact.set(level.nodeKey, count);
  });
  
  return impact;
}, [selectedVariable]);
```

### Finding Optimal Level

```typescript
const optimalLevel = useMemo(() => {
  if (selectedVariable.allowedLevels.length === 0) return null;
  
  let best = selectedVariable.allowedLevels[0];
  let bestCount = levelImpact.get(best.nodeKey) || 0;
  
  selectedVariable.allowedLevels.forEach(level => {
    const count = levelImpact.get(level.nodeKey) || 0;
    // Prefer higher levels (lower level number) when counts are equal
    if (count > bestCount || (count === bestCount && level.level < best.level)) {
      best = level;
      bestCount = count;
    }
  });
  
  return best;
}, [selectedVariable, levelImpact]);
```

---

## Type Definitions

### GroupedVariableInfo
```typescript
interface GroupedVariableInfo {
  variableName: string;
  affectedDatasets: Array<{
    datasetId: number;
    datasetName: string;
    detail: MissingVariableDetail;
  }>;
  allowedLevels: VariableLevelOption[];
  reason: string;
  schemaError: boolean;
  constraints: string;
}
```

### MissingVariableDetail (existing)
```typescript
interface MissingVariableDetail {
  variableName: string;
  datasetId?: number;
  datasetName?: string;
  targetLevel: number;
  targetNodeKey: string;
  allowedLevels: VariableLevelOption[];
  reason: string;
  schemaError?: boolean;
  affectedDescendantCount?: number;
  totalDescendantDatasets?: number;
}
```

### VariableLevelOption (existing)
```typescript
interface VariableLevelOption {
  level: number;
  label: string;
  nodeKey: string;
  nodeType: 'template' | 'category' | 'dataset';
  nodeId?: number;
  datasetId?: number;
}
```

---

## Component Props

### MissingVariablePanel

**Required Props:**
```typescript
open: boolean;              // Whether dialog is open
detail: MissingVariableDetail | null;  // Single detail (for backward compat)
onClose: () => void;        // Close handler
```

**Optional Props:**
```typescript
details?: MissingVariableDetail[];  // Array of details (preferred)
originLabel?: string;       // Context label (e.g., "Category: Europe")
onNavigateToDataset?: (datasetId: number, variableName: string) => void;
onDefineAtLevel?: (variableName: string, level: VariableLevelOption) => void;
```

**Legacy Props (still supported):**
```typescript
selectedIndex?: number;
onSelectDetail?: (index: number) => void;
canDefineHere?: boolean;
onDefineHere?: () => void;
subtreeOption?: { ... };
onDefineSubtree?: () => void;
schemaError?: boolean;
summary?: MissingNodeSummary;
```

---

## Common Patterns

### Pattern 1: Opening Panel from Tree Node

```typescript
const handleTreeNodeMissingClick = useCallback((treeItem: TreeItemData) => {
  if (!treeItem.missingSummary || !treeItem.hasMissingIndicator) {
    return;
  }

  const detailOptions = treeItem.missingSummary.details.filter(
    detail => !detail.schemaError
  );
  
  if (detailOptions.length === 0) {
    return;
  }

  openMissingPanel(
    detailOptions[0].variableName,
    state,
    variable,
    nodeId,
    datasetId,
    {
      detailOptions,
      summary: treeItem.missingSummary,
      originLabel: treeItem.label
    }
  );
}, [openMissingPanel]);
```

### Pattern 2: Navigating to Dataset

```typescript
const handleDatasetClick = (datasetId: number) => {
  if (onNavigateToDataset) {
    onNavigateToDataset(datasetId, selectedVariable.variableName);
    onClose();  // Close modal after navigation
  }
};
```

### Pattern 3: Defining at Level

```typescript
const handleDefineAtSelectedLevel = () => {
  const level = selectedLevelNodeKey 
    ? selectedVariable.allowedLevels.find(l => l.nodeKey === selectedLevelNodeKey)
    : optimalLevel;
    
  if (level && onDefineAtLevel) {
    onDefineAtLevel(selectedVariable.variableName, level);
    onClose();  // Close modal after defining
  }
};
```

---

## Testing Checklist

### Unit Tests
- [ ] Variable grouping logic
- [ ] Level impact calculation
- [ ] Optimal level selection
- [ ] Dataset filtering by level

### Integration Tests
- [ ] Opening panel from tree node
- [ ] Navigating to dataset
- [ ] Defining variable at level
- [ ] Scroll position preservation
- [ ] Variable highlighting

### E2E Tests
- [ ] Complete workflow: open → select → navigate → define
- [ ] Multiple variables in same context
- [ ] Schema error handling
- [ ] Constraint validation

---

## Debugging Tips

### Enable Debug Logging

Uncomment debug logs in `navigateToDefiningLevel` and related functions:

```typescript
console.log('💾 Saving scroll before navigation:', {
  fromKey: currentKey,
  scrollTop: variableListContainerRef.current.scrollTop
});
```

### Common Issues

**Issue: Navigation doesn't work**
- Check that `data` is loaded
- Verify dataset exists in tree structure
- Check console for warnings

**Issue: Variable not highlighted**
- Verify `data-variable-name` attribute is set on variable elements
- Check timing of scroll/highlight (may need to adjust setTimeout delays)

**Issue: Impact counts are wrong**
- Verify `allowedLevels` data is correct
- Check level filtering logic
- Ensure dataset IDs match

**Issue: Optimal level not selected**
- Check level comparison logic
- Verify `levelImpact` map is populated
- Ensure level numbers are correct

---

## Performance Optimization

### Memoization
All expensive calculations are memoized:
- `groupedVariables` - Only recalculates when `detailOptions` changes
- `levelImpact` - Only recalculates when `selectedVariable` changes
- `optimalLevel` - Only recalculates when `selectedVariable` or `levelImpact` changes

### Lazy Loading
- Tree expansion happens on demand
- Scroll positions cached per level
- Navigation triggered only when needed

### Efficient Updates
- State updates batched where possible
- Minimal re-renders through proper dependency arrays
- No unnecessary API calls

---

## Future Enhancements

### Planned Features
1. **Bulk Actions**: Set multiple variables at once
2. **Smart Defaults**: Pre-fill based on similar variables
3. **Conflict Detection**: Warn about conflicting settings
4. **Preview Mode**: Show impact before applying

### Extension Points
- Add custom level impact calculations
- Implement custom navigation strategies
- Add validation hooks before defining
- Support custom UI themes

---

## Migration from Old Implementation

### No Breaking Changes
The new implementation is fully backward compatible. Old code will continue to work.

### Recommended Updates
1. Pass `onNavigateToDataset` callback for dataset navigation
2. Pass `onDefineAtLevel` callback for level-based definition
3. Remove old `onDefineHere` and `onDefineSubtree` handlers (optional)

### Gradual Migration
You can migrate incrementally:
1. Add new callbacks alongside old ones
2. Test new functionality
3. Remove old callbacks when ready

