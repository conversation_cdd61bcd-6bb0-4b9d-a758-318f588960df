# Missing Variables - Architecture Diagram

## Component Hierarchy

```
VariableTreeView
├── RichTreeView (Tree Navigation)
│   └── CustomTreeItem
│       └── Red Exclamation Mark (!) [onClick → handleTreeNodeMissingClick]
│
├── Variable List Panel
│   └── VariableInputRenderer
│       └── VariableStatusBadge [onMissingClick → openMissingPanel]
│
└── MissingVariablePanel (Modal)
    ├── Variable Selector Dropdown
    ├── Variable Information
    ├── Affected Datasets (Clickable Chips)
    ├── Hierarchy Options (Radio Cards)
    └── Action Buttons
```

## Data Flow Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│ User clicks red exclamation mark on tree node                   │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│ handleTreeNodeMissingClick(treeItem)                            │
│ • Extract missingSummary from treeItem                          │
│ • Filter out schema errors                                      │
│ • Get variable state and definition                             │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│ openMissingPanel(variableName, state, variable, ...)            │
│ • Create MissingPanelState                                      │
│ • Pass details array to modal                                   │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│ MissingVariablePanel renders                                    │
│ • Group details by variable name                                │
│ • Calculate level impact                                        │
│ • Determine optimal level                                       │
│ • Display UI                                                    │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│ User interacts with modal                                       │
│                                                                  │
│ Option A: Click dataset chip                                    │
│ ├─→ onNavigateToDataset(datasetId, variableName)               │
│ │   └─→ navigateToDataset()                                    │
│ │       • Find dataset in tree                                 │
│ │       • Expand parent categories                             │
│ │       • Select dataset node                                  │
│ │       • Highlight variable                                   │
│ │       • Close modal                                          │
│                                                                  │
│ Option B: Click "Define at Level" button                        │
│ └─→ onDefineAtLevel(variableName, level)                       │
│     └─→ defineAtLevel()                                        │
│         • Get variable definition                              │
│         • Resolve default value                                │
│         • Update variable at level                             │
│         • Navigate to level                                    │
│         • Highlight variable                                   │
│         • Close modal                                          │
└─────────────────────────────────────────────────────────────────┘
```

## State Management

```
┌─────────────────────────────────────────────────────────────────┐
│ VariableTreeView State                                          │
├─────────────────────────────────────────────────────────────────┤
│ • expandedItems: string[]                                       │
│ • selectedItems: string | null                                  │
│ • treeItems: TreeItemData[]                                     │
│ • missingPanelState: MissingPanelState | null                   │
│ • scrollPositionsRef: Record<string, number>                    │
│ • currentLevelIdRef: string | null                              │
│ • isRestoringScrollRef: boolean                                 │
└─────────────────────────────────────────────────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│ MissingPanelState                                               │
├─────────────────────────────────────────────────────────────────┤
│ • variableName: string                                          │
│ • detail: MissingVariableDetail                                 │
│ • details: MissingVariableDetail[]                              │
│ • selectedIndex: number                                         │
│ • variableDefinition?: VariableWithContext                      │
│ • contextNodeId?: number                                        │
│ • contextDatasetId?: number                                     │
│ • defineHereTarget?: VariableLevelOption                        │
│ • subtreeOption?: { level, missingDescendants, totalDatasets }  │
│ • schemaError?: boolean                                         │
│ • summary?: MissingNodeSummary                                  │
│ • originNodeKey?: string                                        │
│ • originNodeType?: 'category' | 'dataset' | 'global'            │
│ • originLabel?: string                                          │
└─────────────────────────────────────────────────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│ MissingVariablePanel State                                      │
├─────────────────────────────────────────────────────────────────┤
│ • selectedVariableIndex: number                                 │
│ • selectedLevelNodeKey: string | null                           │
│                                                                  │
│ Computed (useMemo):                                             │
│ • groupedVariables: GroupedVariableInfo[]                       │
│ • levelImpact: Map<string, number>                              │
│ • optimalLevel: VariableLevelOption | null                      │
└─────────────────────────────────────────────────────────────────┘
```

## Data Transformation Pipeline

```
┌─────────────────────────────────────────────────────────────────┐
│ Input: MissingVariableDetail[]                                  │
│ [                                                                │
│   { variableName: "LEVERARM_X", datasetId: 1, datasetName: ... },│
│   { variableName: "LEVERARM_X", datasetId: 2, datasetName: ... },│
│   { variableName: "LEVERARM_Y", datasetId: 1, datasetName: ... } │
│ ]                                                                │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼ Group by variableName
┌─────────────────────────────────────────────────────────────────┐
│ Grouped: Map<string, GroupedVariableInfo>                       │
│ {                                                                │
│   "LEVERARM_X": {                                               │
│     variableName: "LEVERARM_X",                                 │
│     affectedDatasets: [                                         │
│       { datasetId: 1, datasetName: "Dataset1", detail: {...} }, │
│       { datasetId: 2, datasetName: "Dataset2", detail: {...} }  │
│     ],                                                           │
│     allowedLevels: [...],                                       │
│     reason: "...",                                              │
│     schemaError: false,                                         │
│     constraints: "Global, Category"                             │
│   },                                                             │
│   "LEVERARM_Y": { ... }                                         │
│ }                                                                │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼ Calculate impact for each level
┌─────────────────────────────────────────────────────────────────┐
│ Level Impact: Map<string, number>                               │
│ {                                                                │
│   "global-root": 2,        // Template affects 2 datasets       │
│   "category-5": 2,         // Category affects 2 datasets       │
│   "category-10": 1,        // Sub-category affects 1 dataset    │
│   "dataset-1": 1,          // Dataset affects 1 dataset         │
│   "dataset-2": 1           // Dataset affects 1 dataset         │
│ }                                                                │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼ Find optimal level
┌─────────────────────────────────────────────────────────────────┐
│ Optimal Level: VariableLevelOption                              │
│ {                                                                │
│   level: 0,                                                      │
│   label: "Global (Template)",                                   │
│   nodeKey: "global-root",                                       │
│   nodeType: "template"                                          │
│ }                                                                │
│ (Highest level that affects the most datasets)                  │
└─────────────────────────────────────────────────────────────────┘
```

## Navigation Flow

### Navigate to Dataset

```
navigateToDataset(datasetId: 123, variableName: "LEVERARM_X")
│
├─→ Find dataset in tree structure
│   └─→ findDatasetPath(tree, 123)
│       └─→ Returns: { dataset, path: [category1, category2] }
│
├─→ Build expansion path
│   └─→ ["global-root", "category-1", "category-2", "dataset-123"]
│
├─→ Expand tree nodes
│   └─→ setExpandedItems([...prev, ...pathToExpand])
│
├─→ Save current scroll position
│   └─→ scrollPositionsRef.current[currentKey] = scrollTop
│
├─→ Select dataset node
│   └─→ setSelectedItems("dataset-123")
│
├─→ Restore scroll position for dataset level
│   └─→ variableListContainerRef.current.scrollTop = savedScroll
│
└─→ Scroll to and highlight variable
    └─→ Find element: document.querySelector(`[data-variable-name="LEVERARM_X"]`)
    └─→ Scroll into view with smooth animation
    └─→ Apply highlight effect (yellow background fade)
```

### Define at Level

```
defineAtLevel(variableName: "LEVERARM_X", level: {...})
│
├─→ Get variable definition
│   └─→ getVariablesByName("LEVERARM_X")[0]
│
├─→ Resolve default value
│   └─→ resolveSchemaDefault(variable)
│
├─→ Update variable at level
│   ├─→ If template: updateVariable(..., undefined, undefined)
│   ├─→ If category: updateVariable(..., nodeId, 'category')
│   └─→ If dataset: updateVariable(..., datasetId, 'dataset')
│
├─→ Navigate to level
│   ├─→ If template: Select "global-root"
│   ├─→ If category: Find category path, expand, select
│   └─→ If dataset: Call navigateToDataset()
│
├─→ Highlight variable
│   └─→ Same as navigateToDataset
│
└─→ Focus variable for editing
    └─→ focusVariable(variableName)
```

## UI Component Structure

```
<Dialog maxWidth="md">
  <DialogTitle>
    Missing Variables
  </DialogTitle>
  
  <DialogContent>
    {/* Context Label */}
    <Typography>Context: Category: Europe</Typography>
    
    {/* Variable Selector (if multiple variables) */}
    <TextField select>
      <MenuItem>LEVERARM_X (3 datasets)</MenuItem>
      <MenuItem>LEVERARM_Y (2 datasets)</MenuItem>
    </TextField>
    
    {/* Variable Information */}
    <Box>
      <Typography variant="h6">LEVERARM_X</Typography>
      <Typography>Required variable is not defined...</Typography>
      <Typography>Configurable at: Global, Category</Typography>
    </Box>
    
    {/* Schema Error Alert (if applicable) */}
    <Alert severity="error">
      Schema configuration issue...
    </Alert>
    
    {/* Affected Datasets */}
    <Box>
      <Typography>Affected Datasets (3)</Typography>
      <Box>
        <Chip onClick={() => navigateToDataset(1)}>Dataset1</Chip>
        <Chip onClick={() => navigateToDataset(2)}>Dataset2</Chip>
        <Chip onClick={() => navigateToDataset(3)}>Dataset3</Chip>
      </Box>
      <Typography variant="caption">
        Click a dataset to navigate to it in the tree
      </Typography>
    </Box>
    
    {/* Hierarchy Options */}
    <Box>
      <Typography>Where to Define This Variable</Typography>
      <Typography variant="caption">
        Choose the hierarchy level...
      </Typography>
      
      <RadioGroup>
        {/* Template Level */}
        <Card variant="outlined" highlighted>
          <CardActionArea>
            <CardContent>
              <FormControlLabel
                control={<Radio />}
                label={
                  <Box>
                    <Typography>
                      Global (Template)
                      <Chip label="Recommended" />
                    </Typography>
                    <Typography variant="caption">
                      Will resolve 3 of 3 affected datasets
                    </Typography>
                  </Box>
                }
              />
            </CardContent>
          </CardActionArea>
        </Card>
        
        {/* Category Level */}
        <Card variant="outlined">
          <CardActionArea>
            <CardContent>
              <FormControlLabel
                control={<Radio />}
                label={
                  <Box>
                    <Typography>Category: Europe</Typography>
                    <Typography variant="caption">
                      Will resolve 3 of 3 affected datasets
                    </Typography>
                  </Box>
                }
              />
            </CardContent>
          </CardActionArea>
        </Card>
      </RadioGroup>
    </Box>
  </DialogContent>
  
  <DialogActions>
    <Button onClick={onClose}>Close</Button>
    <Button onClick={handleDefineAtSelectedLevel} variant="contained">
      Define at Global (Template)
    </Button>
  </DialogActions>
</Dialog>
```

## Performance Optimizations

### Memoization Strategy

```typescript
// Expensive: Group variables (only when details change)
const groupedVariables = useMemo(() => {
  // Grouping logic
}, [detailOptions]);

// Expensive: Calculate impact (only when selected variable changes)
const levelImpact = useMemo(() => {
  // Impact calculation
}, [selectedVariable]);

// Expensive: Find optimal (only when variable or impact changes)
const optimalLevel = useMemo(() => {
  // Optimal level selection
}, [selectedVariable, levelImpact]);
```

### Scroll Position Management

```typescript
// Save scroll position before navigation
scrollPositionsRef.current[currentKey] = scrollTop;

// Restore scroll position after navigation
variableListContainerRef.current.scrollTop = savedScroll;

// Prevent scroll events during restoration
isRestoringScrollRef.current = true;
setTimeout(() => {
  isRestoringScrollRef.current = false;
}, 200);
```

### Lazy Tree Expansion

```typescript
// Only expand nodes in the path to target
const pathToExpand = ['global-root', ...categoryPath, targetNode];
setExpandedItems(prev => {
  const newExpanded = new Set([...prev, ...pathToExpand]);
  return Array.from(newExpanded);
});
```

---

This architecture provides a clean separation of concerns, efficient data processing, and a smooth user experience with proper state management and performance optimizations.

