import { selectVariableForContext } from '@/lib/utils/variableSelection';
import { VariableWithContext } from '@/types/variable';

describe('selectVariableForContext', () => {
  const templateVariable: VariableWithContext = {
    name: 'speed_limit',
    source_level: 'Template',
    is_active: true,
    is_overridden: false,
    value: 30,
  };

  const categoryVariable: VariableWithContext = {
    name: 'speed_limit',
    source_level: 'Category',
    is_active: true,
    is_overridden: true,
    value: 40,
    category_id: 11,
  };

  const datasetVariable: VariableWithContext = {
    name: 'speed_limit',
    source_level: 'Dataset',
    is_active: true,
    is_overridden: true,
    value: 50,
    category_id: 11,
    dataset_id: 22,
  };

  it('returns dataset variable when dataset id matches', () => {
    const result = selectVariableForContext(
      [templateVariable, categoryVariable, datasetVariable],
      11,
      22
    );

    expect(result).toBe(datasetVariable);
  });

  it('falls back to category variable when dataset override is missing', () => {
    const result = selectVariableForContext(
      [templateVariable, categoryVariable],
      11,
      99
    );

    expect(result).toBe(categoryVariable);
  });

  it('returns template variable when no contextual overrides exist', () => {
    const result = selectVariableForContext([templateVariable], undefined, undefined);

    expect(result).toBe(templateVariable);
  });

  it('returns first variable when nothing matches', () => {
    const fallbackVariable: VariableWithContext = {
      name: 'speed_limit',
      source_level: 'Category',
      is_active: true,
      is_overridden: false,
      value: 35,
      category_id: 33,
    };

    const result = selectVariableForContext([fallbackVariable], 99, 88);

    expect(result).toBe(fallbackVariable);
  });
});

