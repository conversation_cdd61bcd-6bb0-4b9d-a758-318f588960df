import { areValuesEqual } from '@/lib/utils/valueComparison';

describe('areValuesEqual', () => {
  it('returns true for identical primitives', () => {
    expect(areValuesEqual('alpha', 'alpha')).toBe(true);
    expect(areValuesEqual(42, 42)).toBe(true);
    expect(areValuesEqual(true, true)).toBe(true);
  });

  it('returns false for different primitive values', () => {
    expect(areValuesEqual('alpha', 'beta')).toBe(false);
    expect(areValuesEqual(42, 7)).toBe(false);
    expect(areValuesEqual(true, false)).toBe(false);
  });

  it('treats NaN values as equal', () => {
    expect(areValuesEqual(NaN, NaN)).toBe(true);
  });

  it('returns true for deeply equal objects and arrays', () => {
    const left = { foo: 'bar', nested: { value: 1 }, list: [1, 2, 3] };
    const right = { foo: 'bar', nested: { value: 1 }, list: [1, 2, 3] };
    expect(areValuesEqual(left, right)).toBe(true);
  });

  it('returns false when object structures differ', () => {
    const left = { foo: 'bar', nested: { value: 1 } };
    const right = { foo: 'bar', nested: { value: 2 } };
    expect(areValuesEqual(left, right)).toBe(false);
  });
});

