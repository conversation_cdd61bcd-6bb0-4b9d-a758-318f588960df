# Missing Variables Redesign - Testing Checklist

## 🚀 Quick Start

The development server is running at: **http://localhost:3001**

Navigate to the Variable Tree page to test the new Missing Variables functionality.

---

## ✅ Basic Functionality Tests

### 1. Opening the Missing Variables Panel

- [ ] **Test 1.1**: Click red exclamation mark (!) on a category with missing variables
  - Expected: <PERSON>dal opens showing all missing variables in that category
  - Expected: Context label shows the category name

- [ ] **Test 1.2**: Click red exclamation mark (!) on a dataset with missing variables
  - Expected: <PERSON><PERSON> opens showing missing variables for that dataset
  - Expected: Context label shows the dataset name

- [ ] **Test 1.3**: Click "Missing" badge on a variable in the variable list
  - Expected: Modal opens focused on that specific variable
  - Expected: Variable information is displayed

### 2. Variable Selector Dropdown

- [ ] **Test 2.1**: Open panel with multiple missing variables
  - Expected: Dropdown appears with unique variable names
  - Expected: Each entry shows dataset count: "VARIABLE_NAME (X datasets)"

- [ ] **Test 2.2**: Select different variables from dropdown
  - Expected: Panel updates to show selected variable's information
  - Expected: Affected datasets list updates
  - Expected: Hierarchy options update

- [ ] **Test 2.3**: Open panel with single missing variable
  - Expected: Dropdown does not appear
  - Expected: Variable information is shown directly

### 3. Affected Datasets Section

- [ ] **Test 3.1**: View affected datasets list
  - Expected: All affected datasets shown as chips
  - Expected: Dataset count is accurate
  - Expected: Chips have hover effect

- [ ] **Test 3.2**: Click on a dataset chip (group expanded)
  - Expected: Modal closes
  - Expected: Tree expands to show the dataset
  - Expected: Dataset is selected
  - Expected: Variable is highlighted in the variable list
  - Expected: Smooth scroll to variable

- [ ] **Test 3.2b**: Click on a dataset chip (group collapsed)
  - Expected: Modal closes
  - Expected: Tree expands to show the dataset
  - Expected: Dataset is selected
  - Expected: Group accordion expands automatically
  - Expected: Variable is highlighted in the variable list
  - Expected: Smooth scroll to variable

- [ ] **Test 3.3**: Click multiple dataset chips in sequence
  - Expected: Each click navigates to the correct dataset
  - Expected: Variable highlighting works each time
  - Expected: Scroll position is preserved correctly
  - Expected: Groups expand as needed

### 4. Hierarchy Options

- [ ] **Test 4.1**: View hierarchy options
  - Expected: All allowed levels are shown as radio cards
  - Expected: Recommended level is highlighted
  - Expected: "Recommended" badge appears on optimal level
  - Expected: Impact counts are displayed for each level

- [ ] **Test 4.2**: Select different hierarchy levels
  - Expected: Radio button updates
  - Expected: "Define at" button label updates
  - Expected: Card selection is visually indicated

- [ ] **Test 4.3**: Verify impact counts
  - Expected: Template level shows total affected datasets
  - Expected: Category levels show correct subset counts
  - Expected: Dataset levels show 1

### 5. Define at Level Button

- [ ] **Test 5.1**: Click "Define at [Recommended Level]" (group expanded)
  - Expected: Modal closes
  - Expected: Tree navigates to that level
  - Expected: Variable is highlighted
  - Expected: Variable is ready for editing
  - Expected: Smooth scroll to variable

- [ ] **Test 5.1b**: Click "Define at [Recommended Level]" (group collapsed)
  - Expected: Modal closes
  - Expected: Tree navigates to that level
  - Expected: Group accordion expands automatically
  - Expected: Variable is highlighted
  - Expected: Variable is ready for editing
  - Expected: Smooth scroll to variable

- [ ] **Test 5.2**: Select a different level and click "Define at [Selected Level]"
  - Expected: Navigates to the selected level (not recommended)
  - Expected: Group expands if needed
  - Expected: Variable is highlighted
  - Expected: Correct level is selected in tree

- [ ] **Test 5.3**: Define at Template level
  - Expected: Navigates to "Global" node
  - Expected: Group expands if needed
  - Expected: Variable is highlighted in global variables list

- [ ] **Test 5.4**: Define at Category level
  - Expected: Navigates to correct category
  - Expected: Category is expanded and selected
  - Expected: Group expands if needed
  - Expected: Variable is highlighted

- [ ] **Test 5.5**: Define at Dataset level
  - Expected: Navigates to correct dataset
  - Expected: All parent categories are expanded
  - Expected: Group expands if needed
  - Expected: Variable is highlighted

---

## 🎯 Inline Editing Tests

### 6. Basic Inline Editor

- [ ] **Test 6.1**: Select template level
  - Expected: Inline editor appears
  - Expected: Single input component shown
  - Expected: No context switcher (only 1 context)
  - Expected: "Save & Close" button visible

- [ ] **Test 6.2**: Edit value in inline editor
  - Expected: Input component works correctly
  - Expected: Value changes are tracked
  - Expected: No orange dot (not saved yet)

- [ ] **Test 6.3**: Save value at template level
  - Expected: "Save Value" button works
  - Expected: Value is saved to backend
  - Expected: Checkmark appears
  - Expected: Can close modal

- [ ] **Test 6.4**: Save & Close button
  - Expected: Saves value
  - Expected: Closes modal immediately
  - Expected: Variable is resolved

- [ ] **Test 6.5**: Navigate to Level button
  - Expected: Button visible when inline editor shown
  - Expected: Closes modal
  - Expected: Navigates to selected level
  - Expected: Does not save value

### 7. Context Switching (2-10 Contexts)

- [ ] **Test 7.1**: Select dataset level with 3 datasets
  - Expected: Inline editor appears
  - Expected: 3 tabs shown
  - Expected: First tab selected by default
  - Expected: Progress indicator: "0 of 3 configured"

- [ ] **Test 7.2**: Switch between tabs
  - Expected: Tab switching works smoothly
  - Expected: Input component updates for new context
  - Expected: Previous values preserved

- [ ] **Test 7.3**: Edit and save first context
  - Expected: Value edits work
  - Expected: "Save Value" button saves
  - Expected: Checkmark appears on tab 1
  - Expected: Progress: "1 of 3 configured"

- [ ] **Test 7.4**: Unsaved changes indicator
  - Expected: Edit value in tab 2
  - Expected: Orange dot appears on tab 2
  - Expected: Switch to tab 3
  - Expected: Orange dot still on tab 2
  - Expected: No dot on tab 3

- [ ] **Test 7.5**: Save all contexts
  - Expected: Save each context individually
  - Expected: All tabs show checkmarks
  - Expected: Progress: "3 of 3 configured"
  - Expected: "Save & Close" button enabled

- [ ] **Test 7.6**: Tab visual indicators
  - Expected: Saved tab shows ✓ checkmark
  - Expected: Unsaved edited tab shows 🟠 dot
  - Expected: Unedited tab shows no indicator
  - Expected: Active tab is highlighted

### 8. Context Switching (11+ Contexts)

- [ ] **Test 8.1**: Select dataset level with 15 datasets
  - Expected: Inline editor appears
  - Expected: Compact selector shown (not tabs)
  - Expected: Shows "[<] Dataset 1 of 15 [>]"
  - Expected: Dropdown selector visible

- [ ] **Test 8.2**: Navigate with prev/next buttons
  - Expected: [>] button goes to next dataset
  - Expected: [<] button goes to previous dataset
  - Expected: [<] disabled on first dataset
  - Expected: [>] disabled on last dataset

- [ ] **Test 8.3**: Navigate with dropdown
  - Expected: Dropdown shows all 15 datasets
  - Expected: Saved datasets show ✓ in dropdown
  - Expected: Selecting dataset switches context
  - Expected: Input updates correctly

- [ ] **Test 8.4**: Save multiple contexts
  - Expected: Save dataset 1
  - Expected: Navigate to dataset 5
  - Expected: Save dataset 5
  - Expected: Dropdown shows ✓ on datasets 1 and 5
  - Expected: Progress: "2 of 15 configured"

### 9. State Management

- [ ] **Test 9.1**: Switch variables
  - Expected: Select different variable from dropdown
  - Expected: Inline editor resets
  - Expected: Edited values cleared
  - Expected: Saved contexts cleared
  - Expected: Context index resets to 0

- [ ] **Test 9.2**: Switch levels
  - Expected: Change from Dataset to Category level
  - Expected: Context switcher updates
  - Expected: Shows correct number of contexts
  - Expected: Context index resets to 0

- [ ] **Test 9.3**: Edited values persistence
  - Expected: Edit value in context 1
  - Expected: Switch to context 2
  - Expected: Switch back to context 1
  - Expected: Edited value still there (not saved)

- [ ] **Test 9.4**: Saved state persistence
  - Expected: Save value in context 1
  - Expected: Switch to context 2
  - Expected: Switch back to context 1
  - Expected: Checkmark still visible
  - Expected: "Save Value" button disabled (already saved)

### 10. Integration with Existing Features

- [ ] **Test 10.1**: Affected datasets chips
  - Expected: Chips still clickable
  - Expected: Clicking chip navigates to dataset
  - Expected: Modal closes
  - Expected: Tree expands to dataset

- [ ] **Test 10.2**: Hierarchy level cards
  - Expected: Clicking card selects level
  - Expected: Inline editor appears
  - Expected: Radio button updates
  - Expected: Recommended badge still visible

- [ ] **Test 10.3**: Variable selector dropdown
  - Expected: Switching variables works
  - Expected: Inline editor updates
  - Expected: State resets correctly

### 11. Error Handling

- [ ] **Test 11.1**: No getVariablesByName prop
  - Expected: Inline editor not shown
  - Expected: Only "Define at Level" button visible
  - Expected: Old workflow still works

- [ ] **Test 11.2**: No updateVariable prop
  - Expected: Inline editor not shown
  - Expected: Only navigation available

- [ ] **Test 11.3**: Variable not found
  - Expected: Graceful error handling
  - Expected: Error message shown
  - Expected: Modal doesn't crash

- [ ] **Test 11.4**: Save fails
  - Expected: Error message shown
  - Expected: Modal stays open
  - Expected: Can retry save
  - Expected: Unsaved changes preserved

### 12. Accordion Expansion Tests

- [ ] **Test 12.1**: Navigate to variable in collapsed group

- [ ] **Test 6.1**: Navigate to variable in collapsed group
  - Expected: Group expands automatically
  - Expected: Expansion animation is smooth (~350ms)
  - Expected: Variable becomes visible
  - Expected: Scroll-to-variable works correctly

- [ ] **Test 12.2**: Navigate to variable in already expanded group
  - Expected: No unnecessary expansion
  - Expected: Direct scroll to variable
  - Expected: No delay or flickering

- [ ] **Test 12.3**: Navigate to variable in "Variable Tree" group (default)
  - Expected: Works same as custom groups
  - Expected: Group expands if collapsed
  - Expected: Variable is highlighted

- [ ] **Test 12.4**: Navigate to variable in custom group (e.g., "GNSS Position")
  - Expected: Correct group is identified
  - Expected: Group expands if collapsed
  - Expected: Variable is highlighted

- [ ] **Test 12.5**: Rapid navigation between variables in different groups
  - Expected: Each group expands as needed
  - Expected: No race conditions
  - Expected: All navigations complete successfully

- [ ] **Test 12.6**: Navigate to variable at top of collapsed group
  - Expected: Group expands
  - Expected: Variable is visible at top
  - Expected: Scroll position is correct

- [ ] **Test 12.7**: Navigate to variable at bottom of collapsed group
  - Expected: Group expands
  - Expected: Variable is visible at bottom
  - Expected: Scroll position centers variable in view

---

## 🔍 Edge Cases

### 13. Schema Errors

- [ ] **Test 13.1**: Open panel for variable with schema error
  - Expected: Red alert box appears
  - Expected: Error message explains the issue
  - Expected: "Define at" button is disabled
  - Expected: Hierarchy options are not shown
  - Expected: Inline editor not shown

- [ ] **Test 13.2**: Variable with no allowed levels
  - Expected: Constraint information shows "None – please review the schema"
  - Expected: No hierarchy options displayed
  - Expected: Action button is disabled
  - Expected: Inline editor not shown

### 14. Constraints

- [ ] **Test 14.1**: Variable with minLevel constraint
  - Expected: Only levels >= minLevel are shown
  - Expected: Lower levels are filtered out
  - Expected: Inline editor respects constraints

- [ ] **Test 14.2**: Variable with maxLevel constraint
  - Expected: Only levels <= maxLevel are shown
  - Expected: Higher levels are filtered out
  - Expected: Inline editor respects constraints

- [ ] **Test 14.3**: Variable with minLevel = maxLevel (single level allowed)
  - Expected: Only one hierarchy option shown
  - Expected: That level is automatically recommended
  - Expected: Inline editor shows single context

### 15. Multiple Variables

- [ ] **Test 15.1**: Category with 5+ missing variables
  - Expected: Dropdown shows all variables
  - Expected: Can switch between variables smoothly
  - Expected: Each variable shows correct affected datasets
  - Expected: Inline editor resets when switching

- [ ] **Test 15.2**: Same variable missing in multiple datasets
  - Expected: All datasets shown in affected datasets list
  - Expected: Impact counts are correct
  - Expected: Recommended level affects all datasets
  - Expected: Inline editor shows all contexts

### 16. Navigation Edge Cases

- [ ] **Test 16.1**: Navigate to deeply nested dataset
  - Expected: All parent categories expand
  - Expected: Scroll position is correct
  - Expected: Dataset is visible in tree

- [ ] **Test 16.2**: Navigate to dataset in collapsed category
  - Expected: Category expands automatically
  - Expected: Dataset becomes visible
  - Expected: Selection is correct

- [ ] **Test 16.3**: Navigate while tree is already expanded
  - Expected: No unnecessary collapse/expand
  - Expected: Smooth transition
  - Expected: Scroll position preserved

### 17. Performance

- [ ] **Test 17.1**: Open panel with 20+ missing variables
  - Expected: Modal opens quickly (< 500ms)
  - Expected: Dropdown is responsive
  - Expected: No lag when switching variables

- [ ] **Test 10.2**: Navigate to dataset with 100+ variables
  - Expected: Navigation completes quickly
  - Expected: Variable highlighting works
  - Expected: No performance degradation

---

## 🎨 UI/UX Tests

### 11. Visual Design

- [ ] **Test 11.1**: Modal appearance
  - Expected: Clean, professional design
  - Expected: Proper spacing and alignment
  - Expected: Consistent with app theme

- [ ] **Test 11.2**: Recommended level highlighting
  - Expected: Highlighted card has distinct border
  - Expected: Background color is different
  - Expected: "Recommended" badge is visible

- [ ] **Test 11.3**: Hover effects
  - Expected: Dataset chips change on hover
  - Expected: Radio cards respond to hover
  - Expected: Buttons have hover states

### 12. Responsive Design

- [ ] **Test 12.1**: Desktop view (1920x1080)
  - Expected: Modal is well-sized (not too wide)
  - Expected: All content is visible
  - Expected: No horizontal scrolling

- [ ] **Test 12.2**: Laptop view (1366x768)
  - Expected: Modal fits on screen
  - Expected: Content is readable
  - Expected: Vertical scrolling works if needed

- [ ] **Test 12.3**: Tablet view (768x1024)
  - Expected: Modal adapts to smaller width
  - Expected: Chips wrap to multiple rows
  - Expected: Touch targets are adequate

### 13. Accessibility

- [ ] **Test 13.1**: Keyboard navigation
  - Expected: Can tab through all interactive elements
  - Expected: Enter/Space activates buttons
  - Expected: Arrow keys work in radio group

- [ ] **Test 13.2**: Screen reader support
  - Expected: Modal has proper ARIA labels
  - Expected: Buttons have descriptive text
  - Expected: Radio options are announced correctly

- [ ] **Test 13.3**: Focus management
  - Expected: Focus moves to modal when opened
  - Expected: Focus returns to trigger when closed
  - Expected: Focus indicators are visible

---

## 🔄 Integration Tests

### 14. Variable Tree Integration

- [ ] **Test 14.1**: Define variable and verify in tree
  - Expected: Variable appears in tree at correct level
  - Expected: Variable state updates (no longer missing)
  - Expected: Red exclamation mark disappears if all resolved

- [ ] **Test 14.2**: Navigate and edit variable
  - Expected: Can edit variable value after navigation
  - Expected: Save works correctly
  - Expected: Changes persist

- [ ] **Test 14.3**: Multiple missing variables workflow
  - Expected: Can resolve variables one by one
  - Expected: Modal updates as variables are resolved
  - Expected: Final variable resolution closes indicator

### 15. Scroll Position Management

- [ ] **Test 15.1**: Navigate between levels
  - Expected: Scroll position saved for each level
  - Expected: Returning to level restores scroll
  - Expected: No jumping or flickering

- [ ] **Test 15.2**: Navigate to variable at bottom of list
  - Expected: Scrolls to show variable
  - Expected: Variable is centered in view
  - Expected: Smooth scroll animation

### 16. State Management

- [ ] **Test 16.1**: Close and reopen modal
  - Expected: State resets correctly
  - Expected: First variable is selected
  - Expected: Recommended level is selected

- [ ] **Test 16.2**: Switch between tree nodes
  - Expected: Modal updates for new context
  - Expected: Correct variables shown
  - Expected: Correct origin label

---

## 🐛 Bug Prevention

### 17. Error Handling

- [ ] **Test 17.1**: Variable not found in tree
  - Expected: Graceful handling (console warning)
  - Expected: No crash
  - Expected: User sees appropriate message

- [ ] **Test 17.2**: Dataset not found in tree
  - Expected: Console warning logged
  - Expected: Modal stays open
  - Expected: No navigation occurs

- [ ] **Test 17.3**: Invalid level selection
  - Expected: Button is disabled
  - Expected: No action occurs on click

### 18. Data Consistency

- [ ] **Test 18.1**: Verify dataset counts
  - Expected: Dropdown count matches affected datasets list
  - Expected: Impact counts sum correctly
  - Expected: No duplicate datasets

- [ ] **Test 18.2**: Verify level filtering
  - Expected: Only allowed levels are shown
  - Expected: Constraints are respected
  - Expected: No invalid options

---

## 📊 Comparison Tests

### 19. Before vs After

- [ ] **Test 19.1**: Compare dropdown efficiency
  - Before: Count entries in old dropdown
  - After: Count entries in new dropdown
  - Expected: Significant reduction in entries

- [ ] **Test 19.2**: Compare navigation workflow
  - Before: Manual navigation required
  - After: One-click navigation
  - Expected: Faster workflow

- [ ] **Test 19.3**: Compare understanding
  - Before: Unclear which datasets affected
  - After: Clear list of affected datasets
  - Expected: Better user understanding

---

## ✨ Final Verification

### 20. Complete Workflow Test

- [ ] **Test 20.1**: End-to-end workflow
  1. Open Variable Tree page
  2. Find category with missing variables (red !)
  3. Click red exclamation mark
  4. Review missing variables in modal
  5. Select a variable from dropdown
  6. Review affected datasets
  7. Click a dataset chip to navigate
  8. Verify navigation and highlighting
  9. Return to category
  10. Open modal again
  11. Select a hierarchy level
  12. Click "Define at Level"
  13. Verify navigation to level
  14. Edit and save variable
  15. Verify missing indicator updates

- [ ] **Test 20.2**: User satisfaction
  - Expected: Workflow is intuitive
  - Expected: No confusion about what to do
  - Expected: Clear understanding of impact
  - Expected: Efficient problem resolution

---

## 📝 Notes

### Issues Found
_Document any issues discovered during testing:_

1. 
2. 
3. 

### Improvements Suggested
_Document any UX improvements that could be made:_

1. 
2. 
3. 

### Performance Observations
_Note any performance issues or optimizations needed:_

1. 
2. 
3. 

---

## ✅ Sign-off

- [ ] All basic functionality tests passed
- [ ] All edge cases handled correctly
- [ ] UI/UX is polished and professional
- [ ] Performance is acceptable
- [ ] No critical bugs found
- [ ] Ready for production deployment

**Tested by**: _______________
**Date**: _______________
**Version**: 1.0.0

