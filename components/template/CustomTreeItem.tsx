import React from 'react';
import { TreeItem, TreeItemProps } from '@mui/x-tree-view/TreeItem';
import { Box, Tooltip } from '@mui/material';
import PriorityHighIcon from '@mui/icons-material/PriorityHigh';
import { VariableStatusBadge } from './VariableStatusBadge';
import { type VariableContextState, type MissingNodeSummary } from '@/types/variable';

interface CustomTreeItemProps extends TreeItemProps {
  focusedVariable?: string | null;
  getVariableState?: (variableName: string, nodeId?: number, datasetId?: number) => VariableContextState;
  nodeId?: number;
  datasetId?: number;
  // Optional flash highlight for attention (e.g., after template change)
  flash?: boolean;
  missingSummary?: MissingNodeSummary;
  hasMissingIndicator?: boolean;
  onMissingIndicatorClick?: () => void;
  treeNodeType?: 'category' | 'dataset' | 'global';
  nodeLabel?: string;
}

export const CustomTreeItem: React.FC<CustomTreeItemProps> = ({
  focusedVariable,
  getVariableState,
  nodeId,
  datasetId,
  label,
  flash = false,
  missingSummary,
  hasMissingIndicator = false,
  onMissingIndicatorClick,
  treeNodeType,
  nodeLabel,
  ...otherProps
}) => {
  const variableState = focusedVariable && getVariableState
    ? getVariableState(focusedVariable, nodeId, datasetId)
    : null;

  const shouldShowBadge = Boolean(
    focusedVariable &&
      variableState &&
      (variableState.primaryState !== 'not-set' ||
        variableState.definedHere ||
        variableState.required ||
        variableState.totalRelevantDatasets > 0 ||
        variableState.missingDescendants > 0 ||
        variableState.schemaError)
  );

  const badge = shouldShowBadge && variableState && focusedVariable ? (
    <VariableStatusBadge
      variableName={focusedVariable}
      primaryState={variableState.primaryState}
      usage={variableState.usage}
      ratioLabel={variableState.ratioLabel}
      lineageState={variableState.lineageState}
      activeSource={variableState.activeVariable}
      overriddenBy={variableState.overriddenBy}
      size="small"
      showActions={false}
      required={variableState.required}
      showDetails={false}
      missingDetails={variableState.missingDetails}
      descendantMissingCount={variableState.missingDescendants}
      schemaError={variableState.schemaError}
    />
  ) : null;

  const handleIndicatorKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (!onMissingIndicatorClick) return;
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      event.stopPropagation();
      onMissingIndicatorClick();
    }
  };

  const locationLabel = nodeLabel ?? (treeNodeType === 'dataset' ? 'Dataset' : treeNodeType === 'category' ? 'Category' : 'Level');
  const indicatorTitle = missingSummary && missingSummary.missingDatasets > 0 && missingSummary.totalDatasets > 0
    ? `${missingSummary.missingDatasets}/${missingSummary.totalDatasets} datasets missing required values`
    : 'Required variable not defined anywhere';
  const allowedLevelLabels = missingSummary
    ? Array.from(new Set(missingSummary.details.flatMap(detail => detail.allowedLevels?.map(level => level.label) ?? [])))
    : [];
  const levelHint = allowedLevelLabels.length
    ? `Configurable at: ${allowedLevelLabels.join(', ')}`
    : 'No level configured';

  const indicator = hasMissingIndicator && missingSummary && onMissingIndicatorClick ? (
    <Tooltip
      title={`${indicatorTitle} • ${levelHint}`}
    >
      <Box
        role="button"
        tabIndex={0}
        onClick={event => {
          event.stopPropagation();
          onMissingIndicatorClick();
        }}
        onKeyDown={handleIndicatorKeyDown}
        aria-label={`${indicatorTitle} in ${locationLabel}. ${levelHint}`}
        sx={{
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: 20,
          height: 20,
          borderRadius: '50%',
          backgroundColor: 'error.main',
          color: 'common.white',
          fontSize: '0.9rem',
          cursor: 'pointer',
          flexShrink: 0,
          boxShadow: theme => `0 0 0 2px ${theme.palette.common.white}`,
          '&:focus-visible': {
            outline: theme => `2px solid ${theme.palette.error.light}`,
            outlineOffset: 2
          }
        }}
      >
        <PriorityHighIcon fontSize="inherit" aria-hidden="true" />
      </Box>
    </Tooltip>
  ) : null;

  // Create enhanced label with focus indicator
  const enhancedLabel = (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        borderRadius: 1,
        transition: 'background-color 800ms ease, box-shadow 800ms ease',
        ...(flash
          ? {
              backgroundColor: 'rgba(25, 118, 210, 0.12)',
              boxShadow: '0 0 0 3px rgba(25, 118, 210, 0.18) inset'
            }
          : {})
      }}
    >
      {indicator}
      <span>{label}</span>
      {badge}
    </Box>
  );

  return (
    <TreeItem
      label={enhancedLabel}
      {...otherProps}
    />
  );
};

export default CustomTreeItem;
