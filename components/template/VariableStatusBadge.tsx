import React from 'react';
import { <PERSON>, Chip, Tooltip, IconButton, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import EditIcon from '@mui/icons-material/Edit';
import LaunchIcon from '@mui/icons-material/Launch';
import {
  getVariableStateColor,
  getVariableStateGradient,
  getVariableStateLabel,
  type VariableState
} from '../../lib/utils/variableColors';
import {
  VariableUsageCounts,
  VariableLineageState,
  VariableSourceDetail,
  MissingVariableDetail
} from '@/types/variable';

type BadgeState = VariableState | 'not-set';

interface VariableStatusBadgeProps {
  variableName: string;
  primaryState: BadgeState;
  usage: VariableUsageCounts;
  ratioLabel: string;
  lineageState: VariableLineageState;
  activeSource?: VariableSourceDetail | null;
  overriddenBy?: string[];
  size?: 'small' | 'medium';
  showTooltip?: boolean;
  onOverride?: (variableName: string) => void;
  onGoToDefining?: (variableName: string) => void;
  showActions?: boolean;
  currentPath?: string;
  required?: boolean;
  showDetails?: boolean;
  missingDetails?: MissingVariableDetail | null;
  descendantMissingCount?: number;
  onMissingClick?: (variableName: string) => void;
  schemaError?: boolean;
}

const RatioChip = styled(Chip)<{
  chipcolor: string;
  gradient?: string | null;
  lineage: VariableLineageState;
  ispartial?: boolean;
  secondarycolor?: string;
}>(({ theme, chipcolor, gradient, lineage, ispartial, secondarycolor }) => ({
  minWidth: 64,
  fontWeight: 600,
  position: 'relative',
  overflow: 'hidden',
  backgroundColor: gradient && !ispartial ? undefined : chipcolor,
  backgroundImage: gradient && !ispartial ? gradient ?? undefined : undefined,
  backgroundRepeat: 'no-repeat',
  backgroundSize: '100% 100%',
  color: ispartial ? theme.palette.common.white : theme.palette.getContrastText(chipcolor),
  border: lineage === 'inherited' ? `1px dashed ${theme.palette.text.secondary}` : 'none',
  '&::before': ispartial
    ? {
        content: '""',
        position: 'absolute',
        inset: 0,
        backgroundColor: chipcolor,
        clipPath: 'polygon(0 0, 50% 0, 50% 100%, 0 100%)'
      }
    : undefined,
  '&::after': ispartial
    ? {
        content: '""',
        position: 'absolute',
        inset: 0,
        backgroundColor: secondarycolor || chipcolor,
        clipPath: 'polygon(50% 0, 100% 0, 100% 100%, 50% 100%)'
      }
    : undefined,
  '& .MuiChip-label': {
    position: 'relative',
    zIndex: 1,
    paddingLeft: theme.spacing(1.25),
    paddingRight: theme.spacing(1.25)
  }
}));

const buildSecondaryLine = (
  usage: VariableUsageCounts,
  primaryState: BadgeState,
  descendantMissingCount?: number,
  schemaError?: boolean
): string => {
  const segments: string[] = [];

  if (usage.total > 0) {
    segments.push(`${usage.used}/${usage.total} using this level`);
  } else {
    segments.push('No applicable datasets');
  }

  if (usage.overridden > 0) {
    segments.push(`${usage.overridden} override${usage.overridden === 1 ? '' : 's'}`);
  }

  if (usage.missing > 0 && primaryState !== 'missing') {
    segments.push(`${usage.missing} missing`);
  }

  if (schemaError) {
    segments.push('Schema configuration issue');
  } else if (descendantMissingCount && descendantMissingCount > 0 && primaryState !== 'missing') {
    segments.push(`${descendantMissingCount} dataset${descendantMissingCount === 1 ? '' : 's'} missing`);
  }

  return segments.join(' • ');
};

const buildTooltipContent = (
  props: VariableStatusBadgeProps,
  statusLabel: string,
  secondaryLine: string
) => {
  const {
    variableName,
    usage,
    overriddenBy,
    lineageState,
    activeSource,
    currentPath,
    required,
    missingDetails,
    schemaError
  } = props;

  const overrideEntries = overriddenBy ?? [];
  const displayedOverrides = overrideEntries.slice(0, 5);
  const hasMoreOverrides = overrideEntries.length > displayedOverrides.length;

  return (
    <Box sx={{ fontSize: '0.75rem', maxWidth: 280 }}>
      <Typography variant="subtitle2" sx={{ fontSize: '0.8rem', fontWeight: 600 }}>
        {variableName}
      </Typography>
      <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
        Status: {statusLabel}
      </Typography>
      {schemaError && (
        <Typography variant="caption" sx={{ display: 'block', mt: 0.5, color: 'warning.main' }}>
          Required variable cannot be set at any allowed level. Please review schema constraints.
        </Typography>
      )}
      {!schemaError && props.primaryState === 'missing' && missingDetails && (
        <Box sx={{ mt: 0.5 }}>
          <Typography variant="caption" sx={{ display: 'block' }}>
            Required variable not defined at any level.
          </Typography>
          {missingDetails.allowedLevels.length > 0 && (
            <Typography variant="caption" sx={{ display: 'block' }}>
              Can be set at level(s):{' '}
              {missingDetails.allowedLevels.map(level => level.label).join(', ')}
            </Typography>
          )}
        </Box>
      )}
      <Typography variant="caption" sx={{ display: 'block' }}>
        Usage: {usage.used}/{usage.total} use • {usage.overridden} override{usage.overridden === 1 ? '' : 's'} • {usage.missing} missing
      </Typography>
      {required && (
        <Typography variant="caption" sx={{ display: 'block' }}>
          Required: yes
        </Typography>
      )}
      {currentPath && (
        <Typography variant="caption" sx={{ display: 'block' }}>
          Context: {currentPath}
        </Typography>
      )}
      {lineageState === 'inherited' && activeSource?.path && (
        <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
          Inherited from: {activeSource.path}
        </Typography>
      )}
      {displayedOverrides.length > 0 && (
        <Box sx={{ mt: 0.75 }}>
          <Typography variant="caption" sx={{ fontWeight: 600, display: 'block' }}>
            Overrides
          </Typography>
          {displayedOverrides.map((path, idx) => (
            <Typography key={idx} variant="caption" sx={{ display: 'block' }}>
              • {path}
            </Typography>
          ))}
          {hasMoreOverrides && (
            <Typography variant="caption" sx={{ display: 'block' }}>
              +{overrideEntries.length - displayedOverrides.length} more
            </Typography>
          )}
        </Box>
      )}
    </Box>
  );
};

export const VariableStatusBadge: React.FC<VariableStatusBadgeProps> = ({
  variableName,
  primaryState,
  usage,
  ratioLabel,
  lineageState,
  activeSource,
  overriddenBy,
  size = 'small',
  showTooltip = true,
  onOverride,
  onGoToDefining,
  showActions = false,
  currentPath,
  required = false,
  showDetails = true,
  missingDetails = null,
  descendantMissingCount = 0,
  onMissingClick,
  schemaError = false
}) => {
  const color = getVariableStateColor(primaryState as VariableState);
  const gradient = getVariableStateGradient(primaryState as VariableState);
  const baseStatusLabel = getVariableStateLabel(primaryState as VariableState);
  const statusLabel = schemaError ? 'Configuration issue' : baseStatusLabel;
  const secondaryLine = buildSecondaryLine(usage, primaryState, descendantMissingCount, schemaError);

  const isPartial = primaryState === 'partial';
  const partialSecondaryColor = isPartial ? getVariableStateColor('overridden') : undefined;
  const isMissing = primaryState === 'missing';
  const showAggregateDot = !isMissing && descendantMissingCount > 0;

  const handleMissingActivation = (event: React.MouseEvent | React.KeyboardEvent) => {
    if (!isMissing || !onMissingClick) {
      return;
    }

    if ('key' in event) {
      if (event.key !== 'Enter' && event.key !== ' ') {
        return;
      }
      event.preventDefault();
    }

    event.stopPropagation();
    onMissingClick(variableName);
  };

  const chip = (
    <RatioChip
      size={size}
      label={primaryState === 'missing' ? 'Missing' : ratioLabel}
      chipcolor={color}
      gradient={gradient}
      lineage={lineageState}
      ispartial={isPartial}
      secondarycolor={partialSecondaryColor}
      onClick={handleMissingActivation}
      onKeyDown={handleMissingActivation}
      role={isMissing && onMissingClick ? 'button' : undefined}
      tabIndex={isMissing && onMissingClick ? 0 : undefined}
      aria-label={
        isMissing
          ? `Required variable ${variableName} not defined. Activate for resolution options.`
          : schemaError
            ? `Variable ${variableName} has a configuration issue.`
            : undefined
      }
    />
  );

  const actionButtons = showActions
    ? (
        <>
          {lineageState === 'inherited' && onOverride && (
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                onOverride(variableName);
              }}
              sx={{ width: 22, height: 22 }}
              title="Override at this level"
            >
              <EditIcon sx={{ fontSize: 16 }} />
            </IconButton>
          )}
          {activeSource && onGoToDefining && primaryState === 'defined-higher' && (
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                onGoToDefining(variableName);
              }}
              sx={{ width: 22, height: 22 }}
              title="Go to defining level"
            >
              <LaunchIcon sx={{ fontSize: 16 }} />
            </IconButton>
          )}
        </>
      )
    : null;

  const content = (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: showDetails || Boolean(actionButtons) ? 1 : 0
      }}
    >
      {chip}
      {showDetails && (
        <Box sx={{ display: 'flex', flexDirection: 'column', minWidth: 0 }}>
          <Typography variant="caption" sx={{ fontWeight: 600 }}>
            {statusLabel}
          </Typography>
          <Typography variant="caption" color="text.secondary" noWrap>
            {secondaryLine}
          </Typography>
        </Box>
      )}
      {actionButtons}
    </Box>
  );

  if (!showTooltip) {
    return content;
  }

  return (
    <Tooltip
      title={buildTooltipContent(
        {
          variableName,
          primaryState,
          usage,
          ratioLabel,
          lineageState,
          activeSource,
          overriddenBy,
          size,
          showTooltip,
          onOverride,
          onGoToDefining,
          showActions,
          currentPath,
          required,
          missingDetails,
          schemaError
        },
        statusLabel,
        secondaryLine
      )}
      arrow
    >
      <Box sx={{ display: 'inline-flex' }}>
        {content}
      </Box>
    </Tooltip>
  );
};

export default VariableStatusBadge;
