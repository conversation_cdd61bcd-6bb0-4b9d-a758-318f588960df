import React from 'react';
import { <PERSON>, <PERSON>, Tooltip, Button, Typography } from '@mui/material';
import { VariableWithContext, VariableContextState } from '@/types/variable';
import InputRenderer from './InputRenderer';
import { VariableStatusBadge } from './VariableStatusBadge';

interface VariableInputRendererProps {
  variable: VariableWithContext;
  onChange: (variableName: string, newValue: any) => void;
  template_data?: any;
  contextInfo?: {
    nodeId?: number;
    nodeType?: 'category' | 'dataset';
    nodeName?: string;
  };
  showStatusBadge?: boolean;
  variableState?: VariableContextState;
  currentValue?: any; // Current value from state management (includes changes)
  // Indicates there is a local, unsaved override pending for this variable
  pendingOverride?: boolean;
  pendingReset?: boolean;
  // Focus functionality props
  focusVariable?: (variableName: string) => void;
  unfocusVariable?: () => void;
  isVariableFocused?: (variableName: string) => boolean;
  // Override/Reset functionality
  onOverride?: (variableName: string) => void;
  onReset?: (variableName: string) => void;
  onGoToDefining?: (variableName: string) => void;
  onMissingClick?: (variableName: string) => void;
}

/**
 * Renders a Variable Tree variable as an input component
 * Converts VariableWithContext to TemplateVariable format and uses InputRenderer
 */
export const VariableInputRenderer: React.FC<VariableInputRendererProps> = ({
  variable,
  onChange,
  template_data,
  contextInfo,
  showStatusBadge = true,
  variableState,
  currentValue,
  focusVariable,
  unfocusVariable,
  isVariableFocused,
  onOverride,
  onReset,
  onGoToDefining,
  pendingOverride,
  pendingReset,
  onMissingClick
}) => {
  // Compute editability based on current context level and constraints
  const isEditableHere = React.useMemo(() => {
    // Determine current level index from context
    // 0 = Global (template), 1+ = Category depth, 999 = Dataset
    const computeCurrentLevelIndex = (): number => {
      // Dataset context is always 999
      if (contextInfo?.nodeType === 'dataset') return 999;

      // If no node specified (global selection), treat as level 0
      if (!contextInfo?.nodeId) return 0;

      // Category context: look up level from tree data
      try {
        const findNodeById = (nodes: any[], id: number): any | null => {
          for (const n of nodes) {
            if (n.id === id) return n;
            if (n.children) {
              const f = findNodeById(n.children, id);
              if (f) return f;
            }
          }
          return null;
        };
        const node = template_data?.tree && contextInfo?.nodeId
          ? findNodeById(template_data.tree, contextInfo.nodeId)
          : null;
        // Backend stores category node level starting at 1
        return node?.level ?? 1;
      } catch {
        // Fallback to category level 1 if lookup fails
        return 1;
      }
    };

    const currentLevelIndex = computeCurrentLevelIndex();

    // Constraints may live under meta.constraints or gui.constraints
    const constraints = (variable as any)?.meta?.constraints || (variable as any)?.gui?.constraints;
    if (constraints && (typeof constraints === 'object')) {
      const min = typeof constraints.minLevel === 'number' ? constraints.minLevel : 0;
      const max = typeof constraints.maxLevel === 'number' ? constraints.maxLevel : 999;
      return currentLevelIndex >= min && currentLevelIndex <= max;
    }

    // Fallback to server-provided flag if no constraints
    return variable.editable_here !== false; // default true if not specified
  }, [contextInfo?.nodeId, contextInfo?.nodeType, template_data, variable]);
  const isInherited = variableState?.lineageState === 'inherited';
  const effectivePrimaryState = pendingReset
    ? 'defined-higher'
    : ((pendingOverride && isInherited) ? 'active' : variableState?.primaryState);
  const disabledInput = pendingReset
    ? true
    : ((!isEditableHere) || (isInherited && !pendingOverride));

  // Convert VariableWithContext to TemplateVariable format
  // Use currentValue if provided (from state management), otherwise use original value
  const templateVariable = {
    name: variable.name,
    data: currentValue !== undefined ? currentValue : (variable.value || variable.data),
    links: variable.links || [],
    gui: variable.gui
  };



  // If no GUI config, show as status badge only
  if (!variable.gui?.component_id) {
    if (!showStatusBadge) return null;
    
    return (
      <Tooltip title={`${variable.name}: ${JSON.stringify(variable.value)}`}>
        <Chip
          label={variable.name}
          size="small"
          color={variable.is_active ? 'primary' : 'default'}
          variant={variable.is_overridden ? 'outlined' : 'filled'}
        />
      </Tooltip>
    );
  }

  return (
    <Box
      sx={{ mb: 2 }}
      data-variable-name={variable.name}
    >
      {/* Status indicator using proper VariableStatusBadge */}
      {showStatusBadge && variableState && (
        <Box sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
          <Box
            onClick={(e) => {
              e.stopPropagation();
              if (focusVariable && unfocusVariable && isVariableFocused) {
                if (isVariableFocused(variable.name)) {
                  unfocusVariable();
                } else {
                  focusVariable(variable.name);
                }
              }
            }}
            sx={{
              cursor: focusVariable ? 'pointer' : 'default',
              display: 'inline-flex',
              borderRadius: 1,
              boxShadow: isVariableFocused && isVariableFocused(variable.name)
                ? '0 0 0 2px rgba(25, 118, 210, 0.35)'
                : '0 0 0 1px rgba(25, 118, 210, 0.16)',
              transition: 'box-shadow 200ms ease',
              padding: '2px 4px'
            }}
          >
            <VariableStatusBadge
              variableName={variable.name}
              primaryState={effectivePrimaryState || variableState.primaryState}
              usage={variableState.usage}
              ratioLabel={variableState.ratioLabel}
              lineageState={variableState.lineageState}
              activeSource={variableState.activeVariable}
              overriddenBy={variableState.overriddenBy}
              size="small"
              showTooltip={true}
              showActions={true}
              onOverride={onOverride}
              onGoToDefining={onGoToDefining}
              required={variableState.required}
              missingDetails={variableState.missingDetails}
              descendantMissingCount={variableState.missingDescendants}
              schemaError={variableState.schemaError}
              onMissingClick={variableState.primaryState === 'missing' && onMissingClick
                ? () => onMissingClick(variable.name)
                : undefined}
            />
          </Box>
        </Box>
      )}

      {/* Input component */}
      <Box sx={{ position: 'relative' }}>
        <InputRenderer
          variable={templateVariable} // Use templateVariable which already has the correct currentValue
          template_data={template_data}
          onChange={onChange}
          disabled={disabledInput}
        />

        {/* Override indicator */}
        {variable.is_overridden && (
          <Box
            sx={{
              position: 'absolute',
              top: -8,
              right: -8,
              width: 16,
              height: 16,
              borderRadius: '50%',
              backgroundColor: 'warning.main',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '0.6rem',
              color: 'white',
              fontWeight: 'bold'
            }}
          >
            !
          </Box>
        )}
      </Box>

      {/* Override/Reset controls */}
      {(!isEditableHere || (isInherited && !pendingOverride)) && isInherited && onOverride && (
        <Box sx={{ mt: 1 }}>
          <Button
            size="small"
            variant="outlined"
            onClick={() => onOverride(variable.name)}
            sx={{ fontSize: '0.75rem' }}
          >
            Override at this level
          </Button>
        </Box>
      )}

      {(!disabledInput) && (effectivePrimaryState === 'active' || effectivePrimaryState === 'partial') && onReset && (
        <Box sx={{ mt: 1 }}>
          <Button
            size="small"
            variant="text"
            color="secondary"
            onClick={() => onReset(variable.name)}
            sx={{ fontSize: '0.75rem' }}
          >
            Reset to inherited
          </Button>
        </Box>
      )}

      {/* Constraint info for disabled variables */}
      {(!isEditableHere) && variable.meta?.constraints && (
        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
          Editable at levels {variable.meta.constraints.minLevel}-{variable.meta.constraints.maxLevel}
        </Typography>
      )}
    </Box>
  );
};



export default VariableInputRenderer;
