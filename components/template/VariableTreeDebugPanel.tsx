import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  But<PERSON>
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import BugReportIcon from '@mui/icons-material/BugReport';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { VariableUsageStatus, VariableLineageState, VariableUsageCounts } from '@/types/variable';

type SavePlanItem = {
  contextKey: string;
  saveLevel: 'template' | 'category' | 'dataset' | string;
  targetId?: number;
  set: { variableOverrides: Record<string, any> } | null;
  reset: string[];
};

interface VariableTreeDebugPanelProps {
  enabled?: boolean;
  selectedContext?: {
    nodeId?: number;
    datasetId?: number;
    type: 'global' | 'category' | 'dataset';
    label?: string;
  } | null;
  debugInfo: {
    variableChangesObject: Record<string, any>;
    changeList: Array<any>;
    groupedByContext: Array<{
      contextKey: string;
      saveLevel: string;
      targetId?: number;
      changes: any[];
    }>;
    savePlan: SavePlanItem[];
  };
  treeSummary?: {
    templateVarCount: number;
    categoryCount: number;
    datasetCount: number;
    totalNodeVars: number;
  };
  contextStates?: Array<{
    name: string;
    primaryState: VariableUsageStatus | 'defined-higher' | 'not-set';
    lineageState: VariableLineageState;
    ratioLabel: string;
    usage: VariableUsageCounts;
    activeAt?: string;
  }>;
  lastDryRun?: { groups: Array<{ contextKey: string; saveLevel: string; targetId?: number; setResponse?: any; resetResponses?: Array<{ variableName: string; response: any }> }> } | null;
}

export default function VariableTreeDebugPanel({ enabled = true, selectedContext, debugInfo, treeSummary, contextStates, lastDryRun }: VariableTreeDebugPanelProps) {
  const [open, setOpen] = React.useState<boolean>(false);

  if (!enabled) return null;

  const totalChanges = debugInfo.changeList.length;
  const totalGroups = debugInfo.groupedByContext.length;

  const copyJson = (obj: any) => {
    try {
      navigator.clipboard?.writeText(JSON.stringify(obj, null, 2));
    } catch (e) {
      // no-op in non-browser envs
    }
  };

  return (
    <Paper
      elevation={6}
      sx={{
        position: 'fixed',
        top: 12,
        right: 12,
        width: open ? 420 : 220,
        maxHeight: '70vh',
        overflow: 'hidden',
        zIndex: 2000,
        borderRadius: 2,
        bgcolor: 'background.paper',
        border: '1px solid',
        borderColor: 'divider'
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', p: 1, gap: 1, bgcolor: 'action.hover' }}>
        <BugReportIcon fontSize="small" />
        <Typography variant="subtitle2" sx={{ flexGrow: 1 }}>Variable Tree Debug</Typography>
        <Chip size="small" color={totalChanges > 0 ? 'warning' : 'default'} label={`${totalChanges} change${totalChanges===1?'':'s'}`} />
        <IconButton size="small" onClick={() => setOpen(o => !o)} aria-label={open ? 'Collapse' : 'Expand'}>
          {open ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
        </IconButton>
      </Box>
      {open && (
        <Box sx={{ p: 1.5, pt: 1, overflow: 'auto', maxHeight: 'calc(70vh - 44px)' }}>
          {/* Selected context */}
          {selectedContext && (
            <Box sx={{ mb: 1.2 }}>
              <Typography variant="caption" sx={{ fontWeight: 600 }}>Selected Context</Typography>
              <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                {selectedContext.type.toUpperCase()} {selectedContext.type==='dataset' ? `#${selectedContext.datasetId}` : selectedContext.type==='category' ? `#${selectedContext.nodeId}` : ''}
                {selectedContext.label ? ` • ${selectedContext.label}` : ''}
              </Typography>
            </Box>
          )}

          {/* Tree summary */}
          {treeSummary && (
            <Box sx={{ mb: 1 }}>
              <Typography variant="caption" sx={{ fontWeight: 600 }}>Tree Summary</Typography>
              <Stack direction="row" spacing={1} sx={{ mt: 0.5, flexWrap: 'wrap' }}>
                <Chip size="small" label={`Template vars: ${treeSummary.templateVarCount}`} />
                <Chip size="small" label={`Categories: ${treeSummary.categoryCount}`} />
                <Chip size="small" label={`Datasets: ${treeSummary.datasetCount}`} />
                <Chip size="small" label={`Node vars: ${treeSummary.totalNodeVars}`} />
              </Stack>
            </Box>
          )}

          <Divider sx={{ my: 1 }} />

          {/* Context variable states */}
          {contextStates && (
            <Box sx={{ mb: 1 }}>
              <Typography variant="caption" sx={{ fontWeight: 600 }}>Context Variable States</Typography>
              <Box sx={{ mt: 0.5 }}>
                <Stack direction="row" spacing={0.5} sx={{ flexWrap: 'wrap' }}>
                  {contextStates.map((s) => (
                    <Chip
                      key={s.name}
                      size="small"
                      label={`${s.name}: ${s.primaryState} • ${s.ratioLabel}${s.lineageState !== 'undefined' ? ` • ${s.lineageState}` : ''}${s.activeAt ? ` @ ${s.activeAt}` : ''}`}
                      title={`Used ${s.usage.used}/${s.usage.total}, overridden ${s.usage.overridden}, missing ${s.usage.missing}`}
                    />
                  ))}
                </Stack>
              </Box>
            </Box>
          )}

          {/* Grouped changes */}
          <Box sx={{ mb: 1 }}>
            <Typography variant="caption" sx={{ fontWeight: 600 }}>Grouped Changes ({totalGroups})</Typography>
            <Box sx={{ mt: 0.5 }}>
              {debugInfo.groupedByContext.length === 0 && (
                <Typography variant="body2" color="text.secondary">None</Typography>
              )}
              {debugInfo.groupedByContext.map((g) => (
                <Box key={g.contextKey} sx={{ mb: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    {g.saveLevel.toUpperCase()} {g.targetId !== undefined ? `#${g.targetId}` : ''}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">{g.contextKey}</Typography>
                  <Stack direction="row" spacing={0.5} sx={{ mt: 0.5, flexWrap: 'wrap' }}>
                    {g.changes.map((c, idx) => (
                      <Chip key={idx} size="small" label={`${c.variableName}${c.action==='reset' ? ' (reset)' : ''}`} />
                    ))}
                  </Stack>
                </Box>
              ))}
            </Box>
          </Box>

          <Divider sx={{ my: 1 }} />

          {/* Save plan preview */}
          <Box sx={{ mb: 1 }}>
            <Typography variant="caption" sx={{ fontWeight: 600 }}>Save Plan Preview</Typography>
            {debugInfo.savePlan.length === 0 && (
              <Typography variant="body2" color="text.secondary">No pending changes</Typography>
            )}
            {debugInfo.savePlan.map((p) => (
              <Box key={p.contextKey} sx={{ mb: 1, p: 1, border: '1px dashed', borderColor: 'divider', borderRadius: 1 }}>
                <Typography variant="body2">
                  {p.saveLevel.toUpperCase()} {p.targetId !== undefined ? `#${p.targetId}` : ''}
                </Typography>
                <Typography variant="caption" color="text.secondary">{p.contextKey}</Typography>
                {p.set && (
                  <Box sx={{ mt: 0.5 }}>
                    <Typography variant="caption" sx={{ fontWeight: 600 }}>SET</Typography>
                    <Box component="pre" sx={{ m: 0, p: 0.5, bgcolor: 'action.hover', borderRadius: 1, overflow: 'auto' }}>
                      {JSON.stringify(p.set.variableOverrides, null, 2)}
                    </Box>
                  </Box>
                )}
                {p.reset.length > 0 && (
                  <Box sx={{ mt: 0.5 }}>
                    <Typography variant="caption" sx={{ fontWeight: 600 }}>RESET</Typography>
                    <Stack direction="row" spacing={0.5} sx={{ mt: 0.5, flexWrap: 'wrap' }}>
                      {p.reset.map((name) => (
                        <Chip key={name} size="small" label={name} />
                      ))}
                    </Stack>
                  </Box>
                )}
              </Box>
            ))}
            {debugInfo.savePlan.length > 0 && (
              <Button size="small" variant="text" onClick={() => copyJson(debugInfo.savePlan)}>Copy JSON</Button>
            )}
          </Box>

          <Divider sx={{ my: 1 }} />

          {/* Raw variableChanges map */}
          <Box sx={{ mb: 1 }}>
            <Typography variant="caption" sx={{ fontWeight: 600 }}>variableChanges Map</Typography>
            <Box component="pre" sx={{ m: 0, p: 0.5, bgcolor: 'action.hover', borderRadius: 1, maxHeight: 160, overflow: 'auto' }}>
              {JSON.stringify(debugInfo.variableChangesObject, null, 2)}
            </Box>
          </Box>

          {/* Last dry run results */}
          {lastDryRun && (
            <Box sx={{ mb: 1 }}>
              <Typography variant="caption" sx={{ fontWeight: 600 }}>Last Dry Run Result</Typography>
              {lastDryRun.groups.length === 0 ? (
                <Typography variant="body2" color="text.secondary">No groups</Typography>
              ) : lastDryRun.groups.map((g, i) => (
                <Box key={i} sx={{ mb: 1, p: 1, border: '1px dashed', borderColor: 'divider', borderRadius: 1 }}>
                  <Typography variant="body2">{g.saveLevel.toUpperCase()} {g.targetId !== undefined ? `#${g.targetId}` : ''}</Typography>
                  {g.setResponse && (
                    <Box sx={{ mt: 0.5 }}>
                      <Typography variant="caption" sx={{ fontWeight: 600 }}>SET Response</Typography>
                      <Box component="pre" sx={{ m: 0, p: 0.5, bgcolor: 'action.hover', borderRadius: 1, overflow: 'auto' }}>
                        {JSON.stringify(g.setResponse, null, 2)}
                      </Box>
                    </Box>
                  )}
                  {g.resetResponses && g.resetResponses.length > 0 && (
                    <Box sx={{ mt: 0.5 }}>
                      <Typography variant="caption" sx={{ fontWeight: 600 }}>RESET Responses</Typography>
                      <Box component="pre" sx={{ m: 0, p: 0.5, bgcolor: 'action.hover', borderRadius: 1, overflow: 'auto' }}>
                        {JSON.stringify(g.resetResponses, null, 2)}
                      </Box>
                    </Box>
                  )}
                </Box>
              ))}
              <Button size="small" variant="text" onClick={() => { try { navigator.clipboard?.writeText(JSON.stringify(lastDryRun, null, 2)); } catch {} }}>Copy JSON</Button>
            </Box>
          )}
        </Box>
      )}
    </Paper>
  );
}
