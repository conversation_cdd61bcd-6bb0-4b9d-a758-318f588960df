import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  Chip,
  CircularProgress,
  Alert,
  Paper,
  Grid,
  Button,
  Snackbar
} from '@mui/material';
import { RichTreeView } from '@mui/x-tree-view/RichTreeView';
import {
  Save as SaveIcon
} from '@mui/icons-material';
import { useVariableTree } from '../../lib/hooks/useVariableTree';
import { useVariableTreeState } from '../../lib/hooks/useVariableTreeState';
import { selectVariableForContext } from '../../lib/utils/variableSelection';
import { VariableStatusBadge } from './VariableStatusBadge';
import { VariableInputRenderer } from './VariableInputRenderer';
import { CustomTreeItem } from './CustomTreeItem';
import VariableTreeGroupRenderer, { VariableTreeGroupRendererRef } from './VariableTreeGroupRenderer';
import VariableTreeDebugPanel from './VariableTreeDebugPanel';
import MissingVariablePanel from './MissingVariablePanel';
import { MissingVariableDetail, VariableLevelOption, VariableContextState, VariableWithContext, MissingNodeSummary } from '@/types/variable';

interface VariableTreeViewProps {
  templateId: number;
  onVariableSelect?: (variableName: string) => void;
  // Optional control (e.g., template selector) to render in the left panel header
  templateControl?: React.ReactNode;
  // Selected template name for Global node label
  templateName?: string;
  // A key that changes when template selection changes (to trigger highlight)
  templateChangeKey?: string | number;
}

interface TreeItemData {
  id: string;
  nodeKey: string;
  label: string;
  type: 'category' | 'dataset' | 'global';
  variables?: any[];
  children?: TreeItemData[];
  nodeId?: number;
  datasetId?: number;
  missingSummary?: MissingNodeSummary;
  hasMissingIndicator?: boolean;
}

interface MissingPanelState {
  variableName: string;
  detail: MissingVariableDetail;
  details: MissingVariableDetail[];
  selectedIndex: number;
  variableDefinition?: VariableWithContext;
  contextNodeId?: number;
  contextDatasetId?: number;
  defineHereTarget?: VariableLevelOption;
  subtreeOption?: {
    level: VariableLevelOption;
    missingDescendants: number;
    totalDatasets: number;
  };
  schemaError?: boolean;
  summary?: MissingNodeSummary;
  originNodeKey?: string;
  originNodeType?: 'category' | 'dataset' | 'global';
  originLabel?: string;
}

// Build tree structure from the new hierarchical API response with Global root node
const buildTreeFromVariableData = (variableTreeNodes: any[], templateVariables: any[], templateName?: string): TreeItemData[] => {
  const convertNode = (node: any): TreeItemData => {
    const categoryNodeId = `category-${node.id}`;
    const treeItem: TreeItemData = {
      id: categoryNodeId,
      nodeKey: categoryNodeId,
      label: node.description ? `${node.name} (${node.description})` : node.name,
      type: 'category',
      variables: node.variables || [],
      children: [],
      nodeId: node.id
    };

    // Add datasets as children if they exist
    if (node.datasets && Array.isArray(node.datasets)) {
      node.datasets.forEach((dataset: any) => {
        const datasetNodeId = `dataset-${dataset.id}`;
        const datasetItem: TreeItemData = {
          id: datasetNodeId,
          nodeKey: datasetNodeId,
          label: dataset.description ? `${dataset.name} (${dataset.description})` : dataset.name,
          type: 'dataset',
          variables: dataset.variables || [],
          nodeId: node.id,
          datasetId: dataset.id
        };
        treeItem.children!.push(datasetItem);
      });
    }

    // Add child categories recursively if they exist
    if (node.children && Array.isArray(node.children)) {
      node.children.forEach((childNode: any) => {
        const childItem = convertNode(childNode);
        treeItem.children!.push(childItem);
      });
    }

    return treeItem;
  };

  // Create Global root node
  const globalRoot: TreeItemData = {
    id: 'global-root',
    nodeKey: 'global-root',
    label: templateName ? `Global (${templateName})` : 'Global',
    type: 'global',
    variables: templateVariables || [],
    children: variableTreeNodes.map(convertNode)
  };

  return [globalRoot];
};

// Helper function to sort tree items recursively
const sortTreeItems = (items: TreeItemData[]): TreeItemData[] => {
  return items
    .sort((a, b) => a.label.localeCompare(b.label))
    .map(item => ({
      ...item,
      children: item.children ? sortTreeItems(item.children) : undefined
    }));
};

const findTreeNodeById = (items: TreeItemData[], id: string): TreeItemData | null => {
  for (const item of items) {
    if (item.id === id) {
      return item;
    }
    if (item.children) {
      const found = findTreeNodeById(item.children, id);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

const getContextNodeType = (node: TreeItemData | null): 'category' | 'dataset' | undefined => {
  if (!node || node.type === 'global') {
    return undefined;
  }
  return node.type;
};

export const VariableTreeView: React.FC<VariableTreeViewProps> = ({
  templateId,
  onVariableSelect,
  templateControl,
  templateName,
  templateChangeKey
}) => {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [selectedItems, setSelectedItems] = useState<string | null>(null);
  const [treeItems, setTreeItems] = useState<TreeItemData[]>([]);
  const [treeLoading, setTreeLoading] = useState(false);
  const [treeError, setTreeError] = useState<string | null>(null);
  const [showInputs, setShowInputs] = useState(true);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [showErrorSnackbar, setShowErrorSnackbar] = useState(false);
  const [flashGlobal, setFlashGlobal] = useState(false);
  const [lastDryRun, setLastDryRun] = useState<null | { groups: Array<{ contextKey: string; saveLevel: string; targetId?: number; setResponse?: any; resetResponses?: Array<{ variableName: string; response: any }> }> }>(null);
  const [missingPanelState, setMissingPanelState] = useState<MissingPanelState | null>(null);
  const firstTemplateRenderRef = useRef(true);

  // Right-panel scroll management: per-level scroll positions
  const variableListContainerRef = useRef<HTMLDivElement | null>(null);
  const scrollPositionsRef = useRef<Record<string, number>>({});
  const currentLevelIdRef = useRef<string | null>(null);
  const isRestoringScrollRef = useRef<boolean>(false);

  // Group accordion refs: Map of groupName -> ref
  const groupRefsRef = useRef<Map<string, React.RefObject<VariableTreeGroupRendererRef>>>(new Map());

  const selectedNode = React.useMemo(() => {
    if (!selectedItems) {
      return null;
    }
    return findTreeNodeById(treeItems, selectedItems);
  }, [selectedItems, treeItems]);


  const getLevelKey = useCallback((node: TreeItemData | null, explicitId?: string | null) => {
    if (explicitId) return explicitId;
    if (!node) return 'none';
    return node.id; // ids are already like 'global-root' | 'category-<id>' | 'dataset-<id>'
  }, []);

  const {
    data,
    loading,
    error,
    refetch,
    getVariablesByName,
    getVariableState,
    getVariableStateForContext,
    focusVariable,
    unfocusVariable,
    isVariableFocused,
    focusedVariable,
    getInheritedValueForContext,
    getNodeMissingSummary
  } = useVariableTree({ templateId });

  // Helper function to find which group contains a variable and expand it
  const expandGroupForVariable = useCallback((variableName: string): Promise<void> => {
    return new Promise((resolve) => {
      // Find the variable to determine its group
      const variables = getVariablesByName(variableName);
      if (variables.length === 0) {
        console.warn(`Variable ${variableName} not found`);
        resolve();
        return;
      }

      const variable = variables[0];
      const groupName = variable.gui?.group || 'Variable Tree';

      // Get the ref for this group
      const groupRef = groupRefsRef.current.get(groupName);
      if (!groupRef || !groupRef.current) {
        console.warn(`Group ref for "${groupName}" not found`);
        resolve();
        return;
      }

      // Check if already expanded
      if (groupRef.current.isExpanded()) {
        resolve();
        return;
      }

      // Expand the group
      groupRef.current.expandGroup();

      // Wait for accordion expansion animation (MUI default is ~300ms)
      setTimeout(() => {
        resolve();
      }, 350);
    });
  }, [getVariablesByName]);

  // Helper function to scroll to and highlight a variable
  const scrollToAndHighlightVariable = useCallback((variableName: string, delay: number = 150) => {
    setTimeout(() => {
      const container = variableListContainerRef.current;
      const el = document.querySelector(`[data-variable-name="${variableName}"]`) as HTMLElement | null;

      if (!el) {
        console.warn(`Element for variable ${variableName} not found in DOM`);
        return;
      }

      if (container && el) {
        const containerRect = container.getBoundingClientRect();
        const elRect = el.getBoundingClientRect();
        const delta = elRect.top - containerRect.top;
        const targetTop = container.scrollTop + delta - container.clientHeight / 2 + elRect.height / 2;
        container.scrollTo({ top: Math.max(0, targetTop), behavior: 'smooth' });

        // Highlight effect
        const originalBackground = el.style.backgroundColor;
        const originalTransition = el.style.transition;
        el.style.backgroundColor = '#fff3cd';
        el.style.transition = 'background-color 0.3s ease';
        setTimeout(() => {
          el.style.backgroundColor = originalBackground;
          el.style.transition = originalTransition;
        }, 3000);
      }
    }, delay);
  }, []);

  const annotateTreeWithMissing = useCallback((items: TreeItemData[]): TreeItemData[] => {
    const decorate = (nodes: TreeItemData[]): TreeItemData[] =>
      nodes.map(node => {
        const children = node.children ? decorate(node.children) : undefined;

        if (node.type === 'global') {
          return {
            ...node,
            children,
            missingSummary: undefined,
            hasMissingIndicator: false
          };
        }

        const summary = getNodeMissingSummary(node.nodeKey);
        const hasMissing = summary.details.some(detail => !detail.schemaError);

        return {
          ...node,
          children,
          missingSummary: summary,
          hasMissingIndicator: hasMissing && (node.type === 'category' || node.type === 'dataset')
        };
      });

    return decorate(items);
  }, [getNodeMissingSummary]);

  // State management for variable changes
  const {
    hasChanges,
    updateVariable,
    resetAllChanges,
    saveChanges,
    dryRunSave,
    resetToInherited,
    isSaving,
    saveError,
    isResetting,
    resetError,
    getVariableValue,
    isVariableChanged,
    isVariableResetPending,
    debugInfo
  } = useVariableTreeState({
    templateId,
    onSaveSuccess: () => {
      console.log('Variables saved successfully');
      setSaveSuccess(true);
      // Refetch the variable tree data to reflect changes
      refetch();
    },
    onSaveError: (error) => {
      console.error('Failed to save variables:', error);
      setShowErrorSnackbar(true);
    }
  });

  const resolveSchemaDefault = useCallback((variable?: VariableWithContext) => {
    if (!variable) return undefined;
    const candidates = [
      variable.gui?.default,
      variable.gui?.default_value,
      variable.meta?.default,
      variable.gui_config?.default
    ];
    return candidates.find(value => value !== undefined);
  }, []);

  const closeMissingPanel = useCallback(() => {
    setMissingPanelState(null);
  }, []);

  const openMissingPanel = useCallback(
    (
      variableName: string,
      state: VariableContextState,
      variable?: VariableWithContext,
      nodeId?: number,
      datasetId?: number,
      options?: {
        detailOverride?: MissingVariableDetail;
        detailOptions?: MissingVariableDetail[];
        detailOptionsInitialIndex?: number;
        originNodeKey?: string;
        originNodeType?: 'category' | 'dataset' | 'global';
        originLabel?: string;
        summary?: MissingNodeSummary;
      }
    ) => {
      const {
        detailOverride,
        detailOptions,
        detailOptionsInitialIndex = 0,
        originNodeKey,
        originNodeType,
        originLabel,
        summary
      } = options ?? {};

      const computedNodeKey = originNodeKey
        ?? (datasetId !== undefined
          ? `dataset-${datasetId}`
          : nodeId !== undefined
            ? `category-${nodeId}`
            : 'global-root');

      const candidateDetails = detailOptions
        ?? (detailOverride
          ? [detailOverride]
          : state.missingDetails
            ? [state.missingDetails]
            : []);

      const normalizedCandidates = candidateDetails.filter(Boolean) as MissingVariableDetail[];
      const safeIndex = Math.max(0, Math.min(detailOptionsInitialIndex, normalizedCandidates.length - 1));
      let resolvedDetail: MissingVariableDetail | null = normalizedCandidates[safeIndex] ?? null;

      if (!resolvedDetail) {
        resolvedDetail = detailOverride ?? state.missingDetails ?? null;
      }

      const baseSchemaError = Boolean(state.schemaError || resolvedDetail?.schemaError);

      if (!resolvedDetail && !baseSchemaError) {
        return;
      }

      const fallbackDetail: MissingVariableDetail = resolvedDetail ?? {
        variableName,
        datasetId,
        datasetName: originLabel ?? selectedNode?.label,
        targetLevel: -1,
        targetNodeKey: computedNodeKey,
        allowedLevels: [],
        reason: 'Required variable cannot be set because of invalid schema configuration.',
        schemaError: true
      };

      const detailList = normalizedCandidates.length > 0 ? normalizedCandidates : [fallbackDetail];
      const selectedIndex = Math.max(0, Math.min(detailOptionsInitialIndex, detailList.length - 1));
      const activeDetail = detailList[selectedIndex] ?? fallbackDetail;

      let defineHereTarget: VariableLevelOption | undefined = activeDetail.allowedLevels.find(level => level.nodeKey === computedNodeKey);
      let subtreeOption: MissingPanelState['subtreeOption'];

      const ancestorCandidate = activeDetail.allowedLevels
        .filter(level => level.nodeKey !== computedNodeKey && level.nodeType !== 'dataset')
        .sort((a, b) => b.level - a.level)[0];

      if (ancestorCandidate) {
        let ancestorState: VariableContextState | null = null;
        if (ancestorCandidate.nodeType === 'template') {
          ancestorState = getVariableState(variableName);
        } else if (ancestorCandidate.nodeType === 'category' && ancestorCandidate.nodeId !== undefined) {
          ancestorState = getVariableStateForContext(variableName, ancestorCandidate.nodeId, undefined);
        } else if (ancestorCandidate.nodeType === 'dataset' && ancestorCandidate.datasetId !== undefined) {
          ancestorState = getVariableStateForContext(variableName, undefined, ancestorCandidate.datasetId);
        }

        if (ancestorState) {
          subtreeOption = {
            level: ancestorCandidate,
            missingDescendants: ancestorState.missingDescendants,
            totalDatasets: ancestorState.totalRelevantDatasets
          };
        }
      }

      const combinedSchemaError = Boolean(baseSchemaError || activeDetail.schemaError);

      setMissingPanelState({
        variableName,
        detail: activeDetail,
        details: detailList,
        selectedIndex,
        variableDefinition: variable,
        contextNodeId: nodeId,
        contextDatasetId: datasetId,
        defineHereTarget,
        subtreeOption,
        schemaError: combinedSchemaError,
        summary,
        originNodeKey: computedNodeKey,
        originNodeType: originNodeType ?? (datasetId !== undefined ? 'dataset' : nodeId !== undefined ? 'category' : undefined),
        originLabel: originLabel ?? selectedNode?.label
      });

      focusVariable(variableName);
    },
    [focusVariable, getVariableState, getVariableStateForContext, selectedNode]
  );

  const handleTreeNodeMissingClick = useCallback(
    (treeItem: TreeItemData) => {
      if (!treeItem.missingSummary || !treeItem.hasMissingIndicator) {
        return;
      }

      const detailOptions = treeItem.missingSummary.details.filter(detail => !detail.schemaError);
      if (detailOptions.length === 0) {
        return;
      }

      const initialDetail = detailOptions[0];
      const allVariables = getVariablesByName(initialDetail.variableName);

      let variable: VariableWithContext | undefined;
      if (treeItem.type === 'dataset' && treeItem.datasetId !== undefined) {
        variable = allVariables.find(v => v.dataset_id === treeItem.datasetId) ?? allVariables[0];
      } else {
        variable = allVariables[0];
      }

      const contextNodeId = treeItem.type === 'category' ? treeItem.nodeId : undefined;
      const contextDatasetId = treeItem.type === 'dataset' ? treeItem.datasetId : undefined;

      const stateForPanel = treeItem.type === 'dataset' && contextDatasetId !== undefined
        ? getVariableStateForContext(initialDetail.variableName, undefined, contextDatasetId)
        : treeItem.type === 'category' && contextNodeId !== undefined
          ? getVariableStateForContext(initialDetail.variableName, contextNodeId, undefined)
          : getVariableState(initialDetail.variableName);

      openMissingPanel(
        initialDetail.variableName,
        stateForPanel,
        variable,
        contextNodeId,
        contextDatasetId,
        {
          detailOverride: initialDetail,
          detailOptions,
          detailOptionsInitialIndex: 0,
          originNodeKey: treeItem.nodeKey,
          originNodeType: treeItem.type === 'global' ? 'global' : treeItem.type,
          originLabel: treeItem.label,
          summary: treeItem.missingSummary
        }
      );
    },
    [getVariableState, getVariableStateForContext, getVariablesByName, openMissingPanel]
  );

  const handleDefineHere = useCallback(() => {
    if (!missingPanelState?.defineHereTarget) {
      return;
    }
    const { defineHereTarget, variableName, variableDefinition } = missingPanelState;
    const fallbackVariable = variableDefinition ?? getVariablesByName(variableName)[0];
    if (!fallbackVariable) {
      closeMissingPanel();
      return;
    }

    const defaultValue = resolveSchemaDefault(fallbackVariable);
    if (defaultValue !== undefined) {
      if (defineHereTarget.nodeType === 'dataset' && defineHereTarget.datasetId !== undefined) {
        updateVariable(variableName, defaultValue, fallbackVariable, defineHereTarget.datasetId, 'dataset', { force: true });
      } else if (defineHereTarget.nodeType === 'category' && defineHereTarget.nodeId !== undefined) {
        updateVariable(variableName, defaultValue, fallbackVariable, defineHereTarget.nodeId, 'category', { force: true });
      } else if (defineHereTarget.nodeType === 'template') {
        updateVariable(variableName, defaultValue, fallbackVariable, undefined, undefined, { force: true });
      }
    }

    focusVariable(variableName);
    closeMissingPanel();
  }, [closeMissingPanel, focusVariable, getVariablesByName, missingPanelState, resolveSchemaDefault, updateVariable]);

  const handleDefineForSubtree = useCallback(() => {
    if (!missingPanelState?.subtreeOption) {
      return;
    }
    const { level } = missingPanelState.subtreeOption;
    const { variableName, variableDefinition } = missingPanelState;
    const fallbackVariable = variableDefinition ?? getVariablesByName(variableName)[0];
    if (!fallbackVariable) {
      closeMissingPanel();
      return;
    }

    const defaultValue = resolveSchemaDefault(fallbackVariable);
    if (defaultValue !== undefined) {
      if (level.nodeType === 'category' && level.nodeId !== undefined) {
        updateVariable(variableName, defaultValue, fallbackVariable, level.nodeId, 'category', { force: true });
      } else if (level.nodeType === 'template') {
        updateVariable(variableName, defaultValue, fallbackVariable, undefined, undefined, { force: true });
      }
    }

    focusVariable(variableName);
    closeMissingPanel();
  }, [closeMissingPanel, focusVariable, getVariablesByName, missingPanelState, resolveSchemaDefault, updateVariable]);

  const handleMissingDetailSelect = useCallback((index: number) => {
    if (!missingPanelState || !missingPanelState.details || missingPanelState.details.length === 0) {
      return;
    }

    if (index < 0 || index >= missingPanelState.details.length) {
      return;
    }

    const nextDetail = missingPanelState.details[index];
    const contextNodeId = missingPanelState.contextNodeId;
    const contextDatasetId = missingPanelState.contextDatasetId;

    const stateForPanel = contextDatasetId !== undefined
      ? getVariableStateForContext(nextDetail.variableName, undefined, contextDatasetId)
      : contextNodeId !== undefined
        ? getVariableStateForContext(nextDetail.variableName, contextNodeId, undefined)
        : getVariableState(nextDetail.variableName);

    const allVariables = getVariablesByName(nextDetail.variableName);
    let variable: VariableWithContext | undefined;
    if (contextDatasetId !== undefined) {
      variable = allVariables.find(v => v.dataset_id === contextDatasetId) ?? allVariables[0];
    } else {
      variable = allVariables[0];
    }

    openMissingPanel(
      nextDetail.variableName,
      stateForPanel,
      variable,
      contextNodeId,
      contextDatasetId,
      {
        detailOverride: nextDetail,
        detailOptions: missingPanelState.details,
        detailOptionsInitialIndex: index,
        originNodeKey: missingPanelState.originNodeKey,
        originNodeType: missingPanelState.originNodeType,
        originLabel: missingPanelState.originLabel,
        summary: missingPanelState.summary
      }
    );
  }, [getVariableState, getVariableStateForContext, getVariablesByName, missingPanelState, openMissingPanel]);
  console.log('Variable tree data:', data);
  // Build tree from variable tree data when it's available

  useEffect(() => {
    if (data?.tree) {
      setTreeLoading(true);
      try {
        // Build tree directly from variable tree data with Global root
        const hierarchicalTree = buildTreeFromVariableData(
          data.tree,
          data.template_variables || [],
          templateName || 'Template'
        );
        console.log('Built hierarchical tree:', hierarchicalTree);
        // Set current level id to default Global root for correct initial scroll handling
        currentLevelIdRef.current = 'global-root';

        const sortedTree = sortTreeItems(hierarchicalTree);
        const annotatedTree = annotateTreeWithMissing(sortedTree);

        setTreeItems(annotatedTree);

        // Auto-expand Global root and first level categories
        const expandIds = ['global-root'];
        if (annotatedTree[0]?.children) {
          expandIds.push(...annotatedTree[0].children.map(item => item.id));
        }
        console.log('Auto-expanding items:', expandIds);
        setExpandedItems(expandIds);

        // Default select Global root
        setSelectedItems('global-root');
        // Initialize current level ref to global-root
        currentLevelIdRef.current = 'global-root';
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
        setTreeError(errorMessage);
        console.error('Error building tree:', err);
      } finally {
        setTreeLoading(false);
        console.log('Tree items:', treeItems);
      }
    }
  }, [annotateTreeWithMissing, data, templateName]);

  // Flash the global node when the template selection changes
  useEffect(() => {
    if (firstTemplateRenderRef.current) {
      firstTemplateRenderRef.current = false;
      return;
    }
    if (templateChangeKey !== undefined) {
      setFlashGlobal(true);
      const t = setTimeout(() => setFlashGlobal(false), 900);
      return () => clearTimeout(t);
    }
  }, [templateChangeKey]);

  // Helper function to collect all unique variable names for a context (including inherited)
  const getAllVariablesForContext = React.useCallback((nodeId?: number, datasetId?: number) => {
    if (!data) return [];

    const allVariableNames = new Set<string>();

    // Add template variables
    data.template_variables.forEach((v: any) => allVariableNames.add(v.name));

    // Find the path to the current context
    const findNodePath = (nodes: any[], targetNodeId: number): any[] => {
      for (const node of nodes) {
        if (node.id === targetNodeId) {
          return [node];
        }
        if (node.children) {
          const childPath = findNodePath(node.children, targetNodeId);
          if (childPath.length > 0) {
            return [node, ...childPath];
          }
        }
      }
      return [];
    };

    if (nodeId) {
      const nodePath = findNodePath(data.tree, nodeId);

      // Add variables from all nodes in the path (inheritance chain)
      nodePath.forEach(node => {
        if (node.variables) {
          node.variables.forEach((v: any) => allVariableNames.add(v.name));
        }
      });

      // If we're looking at a specific dataset, add its variables too
      if (datasetId && nodePath.length > 0) {
        const parentNode = nodePath[nodePath.length - 1];
        const targetDataset = parentNode.datasets?.find((d: any) => d.id === datasetId);
        if (targetDataset && targetDataset.variables) {
          targetDataset.variables.forEach((v: any) => allVariableNames.add(v.name));
        }
      }
    }

    return Array.from(allVariableNames).sort();
  }, [data]);

  // Navigate to the defining level of a variable (context-aware)
  const navigateToDefiningLevel = useCallback((variableName: string) => {
    if (!data) return;

    // Determine current context from the selected node
    const contextNodeId = selectedNode?.type === 'global' ? undefined : selectedNode?.nodeId;
    const contextDatasetId = selectedNode?.type === 'dataset' ? selectedNode?.datasetId : undefined;

    // Use context-aware state to find the actually active/effective variable
    const state = getVariableStateForContext(variableName, contextNodeId, contextDatasetId);
    const active = state.activeVariable; // shape: { variable, level, path, nodeId?, datasetId? }
    if (!active) return;

    const findCategoryLevelById = (nodes: any[], targetId: number): number | null => {
      for (const node of nodes) {
        if (node.id === targetId) {
          return typeof node.level === 'number' ? node.level : null;
        }
        if (node.children) {
          const childLevel = findCategoryLevelById(node.children, targetId);
          if (childLevel !== null) {
            return childLevel;
          }
        }
      }
      return null;
    };

    const currentContextLevel = (() => {
      if (!selectedNode) return 0;
      if (selectedNode.type === 'global') return 0;
      if (selectedNode.type === 'dataset') return 999;
      if (selectedNode.nodeId !== undefined) {
        const level = findCategoryLevelById(data.tree || [], selectedNode.nodeId);
        return level ?? 0;
      }
      return 0;
    })();

    // Helper: find path of category ancestors to a target category id
    const findCategoryPathById = (nodes: any[], targetId: number, acc: any[] = []): any[] | null => {
      for (const node of nodes) {
        const currentPath = [...acc, node];
        if (node.id === targetId) return currentPath;
        if (node.children) {
          const found = findCategoryPathById(node.children, targetId, currentPath);
          if (found) return found;
        }
      }
      return null;
    };

    // Build target selection and expansion path
    let targetNodeId: string = 'global-root';
    let pathToExpand: string[] = ['global-root'];

    const resolveCategoryTarget = (categoryId: number) => {
      const catPath = findCategoryPathById(data.tree || [], categoryId);
      if (catPath && catPath.length > 0) {
        pathToExpand = ['global-root', ...catPath.map((n: any) => `category-${n.id}`)];
      } else {
        pathToExpand = ['global-root', `category-${categoryId}`];
      }
      targetNodeId = `category-${categoryId}`;
    };

    if (active.level === 0 || (!active.nodeId && !active.datasetId)) {
      // Template level
      targetNodeId = 'global-root';
      pathToExpand = ['global-root'];
    } else if (active.nodeId && (active.level ?? 0) <= currentContextLevel) {
      // Category level ancestor: include all ancestor categories
      resolveCategoryTarget(active.nodeId);
    } else if (active.nodeId) {
      // Fallback to the category even if level metadata is missing
      resolveCategoryTarget(active.nodeId);
    } else if (active.datasetId && selectedNode?.type === 'dataset' && active.datasetId === selectedNode.datasetId) {
      // Dataset-level definition only when already at dataset context
      const catPath = active.nodeId ? findCategoryPathById(data.tree || [], active.nodeId) : null;
      if (catPath && catPath.length > 0) {
        pathToExpand = ['global-root', ...catPath.map((n: any) => `category-${n.id}`), `dataset-${active.datasetId}`];
      } else {
        pathToExpand = ['global-root', `dataset-${active.datasetId}`];
      }
      targetNodeId = `dataset-${active.datasetId}`;
    }

    // Expand the path to the target node
    setExpandedItems(prev => {
      const newExpanded = new Set([...prev, ...pathToExpand]);
      return Array.from(newExpanded);
    });

    // Set restoring flag immediately to prevent scroll events during navigation
    isRestoringScrollRef.current = true;

    // Before switching, save current level scroll position
    const currentKey = currentLevelIdRef.current || getLevelKey(selectedNode);
    if (variableListContainerRef.current && currentKey) {
      scrollPositionsRef.current[currentKey] = variableListContainerRef.current.scrollTop;
      // Debug: Saving scroll before navigation
      // console.log('💾 Saving scroll before navigation:', {
      //   fromKey: currentKey,
      //   scrollTop: variableListContainerRef.current.scrollTop
      // });
    }

    // Select the target node
    setSelectedItems(targetNodeId);
    // Update current level id for scroll tracking
    currentLevelIdRef.current = targetNodeId;

    // After selection state updates and right panel renders, restore its saved scroll first
    setTimeout(() => {
      const targetKey = targetNodeId;
      const savedScroll = scrollPositionsRef.current[targetKey] ?? 0;

      // Debug: Restoring scroll for navigation target
      // console.log('🎯 Restoring scroll for navigation target:', {
      //   targetKey,
      //   savedScroll,
      //   allScrollPositions: { ...scrollPositionsRef.current }
      // });

      if (variableListContainerRef.current) {
        // Ensure the flag is set during restoration
        isRestoringScrollRef.current = true;
        variableListContainerRef.current.scrollTop = savedScroll;

        setTimeout(() => {
          isRestoringScrollRef.current = false;
          // console.log('✅ Navigation scroll restoration complete for:', targetKey);
        }, 200);
      }

      // Then scroll the specific variable into view within the right panel container
      setTimeout(() => {
        const container = variableListContainerRef.current;
        const el = document.querySelector(`[data-variable-name="${variableName}"]`) as HTMLElement | null;
        if (container && el) {
          // Compute position relative to container and center it
          const containerRect = container.getBoundingClientRect();
          const elRect = el.getBoundingClientRect();
          const delta = elRect.top - containerRect.top;
          const targetTop = container.scrollTop + delta - container.clientHeight / 2 + elRect.height / 2;
          container.scrollTo({ top: Math.max(0, targetTop), behavior: 'smooth' });

          // Add highlight effect
          const originalBackground = el.style.backgroundColor;
          const originalTransition = el.style.transition;
          el.style.backgroundColor = '#fff3cd';
          el.style.transition = 'background-color 0.3s ease';
          setTimeout(() => {
            el.style.backgroundColor = originalBackground;
            el.style.transition = originalTransition;
          }, 3000);
        }
      }, 150);
    }, 150);

  }, [data, selectedNode, getVariableStateForContext, getLevelKey]);

  // Navigate to a specific dataset and optionally focus a variable
  const navigateToDataset = useCallback((datasetId: number, variableName?: string) => {
    if (!data) return;

    // Helper to find dataset and its category path
    const findDatasetPath = (nodes: any[], targetDatasetId: number, categoryPath: any[] = []): { dataset: any; path: any[] } | null => {
      for (const node of nodes) {
        // Check if this category has the dataset
        if (node.datasets && Array.isArray(node.datasets)) {
          const dataset = node.datasets.find((ds: any) => ds.id === targetDatasetId);
          if (dataset) {
            return { dataset, path: categoryPath };
          }
        }

        // Recursively check children
        if (node.children && Array.isArray(node.children)) {
          const result = findDatasetPath(node.children, targetDatasetId, [...categoryPath, node]);
          if (result) return result;
        }
      }
      return null;
    };

    const result = findDatasetPath(data.tree || [], datasetId);
    if (!result) {
      console.warn(`Dataset ${datasetId} not found in tree`);
      return;
    }

    const { path } = result;

    // Build expansion path
    const pathToExpand = ['global-root', ...path.map((n: any) => `category-${n.id}`), `dataset-${datasetId}`];
    const targetNodeId = `dataset-${datasetId}`;

    // Expand the path
    setExpandedItems(prev => {
      const newExpanded = new Set([...prev, ...pathToExpand]);
      return Array.from(newExpanded);
    });

    // Set restoring flag
    isRestoringScrollRef.current = true;

    // Save current scroll position
    const currentKey = currentLevelIdRef.current || getLevelKey(selectedNode);
    if (variableListContainerRef.current && currentKey) {
      scrollPositionsRef.current[currentKey] = variableListContainerRef.current.scrollTop;
    }

    // Select the dataset node
    setSelectedItems(targetNodeId);
    currentLevelIdRef.current = targetNodeId;

    // Restore scroll and focus variable
    setTimeout(() => {
      const targetKey = targetNodeId;
      const savedScroll = scrollPositionsRef.current[targetKey] ?? 0;

      if (variableListContainerRef.current) {
        isRestoringScrollRef.current = true;
        variableListContainerRef.current.scrollTop = savedScroll;

        setTimeout(() => {
          isRestoringScrollRef.current = false;
        }, 200);
      }

      // Focus the variable if provided
      if (variableName) {
        // First expand the group containing the variable, then scroll to it
        expandGroupForVariable(variableName).then(() => {
          scrollToAndHighlightVariable(variableName, 150);
        });
      }
    }, 150);
  }, [data, selectedNode, getLevelKey, expandGroupForVariable, scrollToAndHighlightVariable]);

  // Define a variable at a specific level and navigate there
  const defineAtLevel = useCallback((variableName: string, level: VariableLevelOption) => {
    const variable = getVariablesByName(variableName)[0];
    if (!variable) {
      console.warn(`Variable ${variableName} not found`);
      return;
    }

    const defaultValue = resolveSchemaDefault(variable);

    // Update variable at the specified level
    if (level.nodeType === 'template') {
      updateVariable(variableName, defaultValue, variable, undefined, undefined, { force: true });
    } else if (level.nodeType === 'category' && level.nodeId !== undefined) {
      updateVariable(variableName, defaultValue, variable, level.nodeId, 'category', { force: true });
    } else if (level.nodeType === 'dataset' && level.datasetId !== undefined) {
      updateVariable(variableName, defaultValue, variable, level.datasetId, 'dataset', { force: true });
    }

    // Navigate to that level
    if (!data) return;

    let targetNodeId: string = 'global-root';
    let pathToExpand: string[] = ['global-root'];

    if (level.nodeType === 'template') {
      targetNodeId = 'global-root';
      pathToExpand = ['global-root'];
    } else if (level.nodeType === 'category' && level.nodeId !== undefined) {
      // Find category path
      const findCategoryPathById = (nodes: any[], targetId: number, acc: any[] = []): any[] | null => {
        for (const node of nodes) {
          const currentPath = [...acc, node];
          if (node.id === targetId) return currentPath;
          if (node.children) {
            const found = findCategoryPathById(node.children, targetId, currentPath);
            if (found) return found;
          }
        }
        return null;
      };

      const catPath = findCategoryPathById(data.tree || [], level.nodeId);
      if (catPath && catPath.length > 0) {
        pathToExpand = ['global-root', ...catPath.map((n: any) => `category-${n.id}`)];
      } else {
        pathToExpand = ['global-root', `category-${level.nodeId}`];
      }
      targetNodeId = `category-${level.nodeId}`;
    } else if (level.nodeType === 'dataset' && level.datasetId !== undefined) {
      // Use navigateToDataset for dataset level
      navigateToDataset(level.datasetId, variableName);
      focusVariable(variableName);
      return;
    }

    // Expand and select
    setExpandedItems(prev => {
      const newExpanded = new Set([...prev, ...pathToExpand]);
      return Array.from(newExpanded);
    });

    isRestoringScrollRef.current = true;

    const currentKey = currentLevelIdRef.current || getLevelKey(selectedNode);
    if (variableListContainerRef.current && currentKey) {
      scrollPositionsRef.current[currentKey] = variableListContainerRef.current.scrollTop;
    }

    setSelectedItems(targetNodeId);
    currentLevelIdRef.current = targetNodeId;

    setTimeout(() => {
      const targetKey = targetNodeId;
      const savedScroll = scrollPositionsRef.current[targetKey] ?? 0;

      if (variableListContainerRef.current) {
        isRestoringScrollRef.current = true;
        variableListContainerRef.current.scrollTop = savedScroll;

        setTimeout(() => {
          isRestoringScrollRef.current = false;
        }, 200);
      }

      // Expand group and scroll to variable
      expandGroupForVariable(variableName).then(() => {
        scrollToAndHighlightVariable(variableName, 150);
      });
    }, 150);

    focusVariable(variableName);
  }, [data, selectedNode, getVariablesByName, resolveSchemaDefault, updateVariable, navigateToDataset, focusVariable, getLevelKey, expandGroupForVariable, scrollToAndHighlightVariable]);

  // Find selected node when selection changes
  React.useEffect(() => {
    if (!selectedItems || !selectedNode) {
      return;
    }

    // Restore per-level scroll position after selection updates
    setTimeout(() => {
      const key = getLevelKey(selectedNode, selectedItems);
      const saved = scrollPositionsRef.current[key] ?? 0;

      // Debug: Restoring scroll position
      // console.log('📍 Restoring scroll position:', {
      //   levelKey: key,
      //   savedScrollTop: saved,
      //   allScrollPositions: { ...scrollPositionsRef.current }
      // });

      if (variableListContainerRef.current) {
        // Ensure the flag is still set during restoration
        isRestoringScrollRef.current = true;
        variableListContainerRef.current.scrollTop = saved;

        // Reset the flag after a longer delay to ensure all scroll events are processed
        setTimeout(() => {
          isRestoringScrollRef.current = false;
          // console.log('✅ Scroll restoration complete for level:', key);
        }, 200);
      }
    }, 0);
  }, [selectedItems, selectedNode, getLevelKey]);

  // Handle ESC key to unfocus variable
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && focusedVariable) {
        unfocusVariable();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [focusedVariable, unfocusVariable]);

  if (loading || treeLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || treeError) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Error loading data: {error || treeError}
        <Box sx={{ mt: 1 }}>
          <Chip
            label="Try again"
            onClick={refetch}
            size="small"
            variant="outlined"
          />
        </Box>
      </Alert>
    );
  }

  if (!data && treeItems.length === 0) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        No data available
      </Alert>
    );
  }

  // Handle click on empty area to unfocus variable
  const handleBackgroundClick = (event: React.MouseEvent) => {
    // Only unfocus if clicking directly on the Paper background
    if (event.target === event.currentTarget && focusedVariable) {
      unfocusVariable();
    }
  };

  return (
    <Paper
      sx={{ p: 2 }}
      onClick={handleBackgroundClick}
    >
      {/* Debug panel */}
      <VariableTreeDebugPanel
        enabled={true}
        selectedContext={selectedNode ? {
          type: selectedNode.type,
          nodeId: selectedNode.nodeId,
          datasetId: selectedNode.datasetId,
          label: selectedNode.label
        } : { type: 'global', label: 'Global' }}
        debugInfo={debugInfo()}
        treeSummary={data ? {
          templateVarCount: Array.isArray(data.template_variables) ? data.template_variables.length : 0,
          categoryCount: (() => {
            const count = (nodes: any[]): number => nodes.reduce((acc, n) => acc + 1 + (n.children ? count(n.children) : 0), 0);
            return count(data.tree || []);
          })(),
          datasetCount: (() => {
            const count = (nodes: any[]): number => nodes.reduce((acc, n) => acc + (n.datasets ? n.datasets.length : 0) + (n.children ? count(n.children) : 0), 0);
            return count(data.tree || []);
          })(),
          totalNodeVars: (() => {
            const count = (nodes: any[]): number => nodes.reduce((acc, n) => acc + (n.variables ? n.variables.length : 0) + (n.children ? count(n.children) : 0), 0);
            return count(data.tree || []);
          })(),
        } : undefined}
        contextStates={(() => {
          if (!data) return undefined;
          const nodeId = selectedNode?.type === 'global' ? undefined : selectedNode?.nodeId;
          const datasetId = selectedNode?.type === 'dataset' ? selectedNode?.datasetId : undefined;
          const names = getAllVariablesForContext(nodeId, datasetId) || [];
          const toState = (name: string) => {
            const s = selectedNode?.type === 'global'
              ? getVariableState(name)
              : getVariableStateForContext(name, nodeId, datasetId);
            const activeAt = s.activeVariable?.level === 0
              ? 'template'
              : s.activeVariable?.level === 999
                ? `dataset#${s.activeVariable?.datasetId ?? ''}`
                : s.activeVariable?.nodeId != null
                  ? `category#${s.activeVariable?.nodeId}`
                  : undefined;
            return {
              name,
              primaryState: s.primaryState,
              lineageState: s.lineageState,
              ratioLabel: s.ratioLabel,
              usage: s.usage,
              activeAt
            } as const;
          };
          return names.map(toState);
        })()}
        lastDryRun={lastDryRun}
      />
      {/* Header with Save Button */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
        <Typography variant="h6">
          Variable Tree (Template ID: {templateId})
        </Typography>

        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          {/* Save/Reset Controls */}
          {hasChanges && (
            <>
              <Button
                variant="contained"
                size="small"
                startIcon={<SaveIcon />}
                onClick={saveChanges}
                disabled={isSaving}
                color="primary"
              >
                {isSaving ? 'Saving...' : 'Save'}
              </Button>
              <Button
                variant="outlined"
                size="small"
                onClick={async () => {
                  try {
                    const res = await dryRunSave();
                    setLastDryRun(res);
                  } catch (e) {
                    console.error('Dry run failed:', e);
                    setShowErrorSnackbar(true);
                  }
                }}
                disabled={isSaving}
              >
                Dry Run
              </Button>
              <Button
                variant="outlined"
                size="small"
                onClick={resetAllChanges}
                disabled={isSaving}
                color="secondary"
              >
                Reset
              </Button>
            </>
          )}

          <Button
            variant="outlined"
            size="small"
            onClick={() => setShowInputs(!showInputs)}
          >
            {showInputs ? 'STATUS BADGES' : 'INPUT COMPONENTS'}
          </Button>
        </Box>
      </Box>

      {/* Save Error */}
      {saveError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          Error saving: {saveError}
        </Alert>
      )}

      {/* Two-column layout */}
      <Grid container spacing={2}>
        {/* Left column: Tree */}
        <Grid item xs={12} md={4} lg={3}>
          <Paper sx={{ p: 2, height: 'calc(100vh - 240px)', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              {/* Only dropdown on this line for space */}
              {templateControl}
            </Box>
            {treeItems.length > 0 ? (
              <RichTreeView
                items={treeItems}
                expandedItems={expandedItems}
                onExpandedItemsChange={(event, itemIds) => setExpandedItems(itemIds)}
                selectedItems={selectedItems}
                onSelectedItemsChange={(event, itemId) => {
                  // Set restoring flag immediately to prevent scroll events during transition
                  isRestoringScrollRef.current = true;

                  // Save current level scroll position before switching
                  // Use currentLevelIdRef instead of selectedNode to get the current key
                  const currentKey = currentLevelIdRef.current || getLevelKey(selectedNode);
                  const currentScrollTop = variableListContainerRef.current?.scrollTop || 0;

                  // Debug: Level switching
                  // console.log('🔄 Level switching:', {
                  //   from: currentKey,
                  //   to: itemId,
                  //   savingScrollTop: currentScrollTop,
                  //   currentScrollPositions: { ...scrollPositionsRef.current }
                  // });

                  if (variableListContainerRef.current && currentKey) {
                    scrollPositionsRef.current[currentKey] = currentScrollTop;
                  }

                  // Update current level id after selection (used by onScroll)
                  const newLevelId = typeof itemId === 'string' ? itemId : String(itemId);
                  currentLevelIdRef.current = newLevelId;

                  setSelectedItems(itemId)
                }}
                slots={{
                  item: CustomTreeItem
                }}
                slotProps={{
                  item: (ownerState: any) => {
                    // Find the tree item data for this node
                    const findTreeItem = (items: TreeItemData[], id: string): TreeItemData | null => {
                      for (const item of items) {
                        if (item.id === id) return item;
                        if (item.children) {
                          const found = findTreeItem(item.children, id);
                          if (found) return found;
                        }
                      }
                      return null;
                    };

                    const treeItem = findTreeItem(treeItems, ownerState.itemId);
                    // Compute current level index at this tree item for constraint filtering
                    const findNodeByIdForLevel = (nodes: any[], id: number): any | null => {
                      for (const n of nodes) {
                        if (n.id === id) return n;
                        if (n.children) {
                          const f = findNodeByIdForLevel(n.children, id);
                          if (f) return f;
                        }
                      }
                      return null;
                    };
                    const handleIndicator = treeItem ? () => handleTreeNodeMissingClick(treeItem) : undefined;
                    return {
                      focusedVariable,
                      getVariableState: getVariableStateForContext,
                      nodeId: treeItem?.nodeId,
                      datasetId: treeItem?.datasetId,
                      flash: ownerState.itemId === 'global-root' ? flashGlobal : false,
                      missingSummary: treeItem?.missingSummary,
                      hasMissingIndicator: treeItem?.hasMissingIndicator,
                      onMissingIndicatorClick: handleIndicator,
                      treeNodeType: treeItem?.type,
                      nodeLabel: treeItem?.label
                    } as any; // Type assertion to bypass strict typing
                  }
                }}
                sx={{
                  flexGrow: 1,
                  maxWidth: '100%',
                  overflowY: 'auto',
                  bgcolor: 'background.paper',
                  borderRadius: 1,
                  p: 1
                }}
              />
            ) : (
              <Alert severity="info">
                No categories found
              </Alert>
            )}
          </Paper>
        </Grid>

        {/* Right column: Settings */}
        <Grid item xs={12} md={8} lg={9}>
          <Paper sx={{ p: 2, height: 'calc(100vh - 240px)', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <Typography variant="subtitle1">
                Settings
              </Typography>
              {selectedNode && (
                <Chip
                  label={selectedNode.type === 'global' ? 'Global' :
                         selectedNode.type === 'dataset' ? 'Dataset' : 'Category'}
                  size="small"
                  color={selectedNode.type === 'global' ? 'primary' :
                         selectedNode.type === 'dataset' ? 'secondary' : 'default'}
                  variant="outlined"
                />
              )}
            </Box>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
              🟢 Active • 🟠 Overridden • 🔘 Inherited from higher level • 🔴 Not set
            </Typography>
            {selectedNode ? (
              <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: 0, flexGrow: 1 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {selectedNode.type === 'dataset' ? 'Dataset' :
                   selectedNode.type === 'global' ? 'Global' : 'Category'}: {selectedNode.label}
                </Typography>
                {(() => {
                  // Get all available variables for this context (including inherited)
                  const nodeId = selectedNode.type === 'global' ? undefined : selectedNode.nodeId;
                  const datasetId = selectedNode.type === 'global' ? undefined : selectedNode.datasetId;


                  const allVariableNames = selectedNode.type === 'global' ?
                    (data?.template_variables || []).map((v: any) => v.name) :
                    getAllVariablesForContext(nodeId, datasetId);

                  return allVariableNames.length > 0 ? (
                    <Box
                      ref={variableListContainerRef}
                      onScroll={(e) => {
                        // Don't save scroll position if we're currently restoring it
                        if (isRestoringScrollRef.current) {
                          // Debug: Ignoring scroll event during restoration
                          // console.log('🚫 Ignoring scroll event during restoration:', {
                          //   scrollTop: (e.currentTarget as HTMLDivElement).scrollTop,
                          //   currentLevelId: currentLevelIdRef.current
                          // });
                          return;
                        }

                        // Persist scroll under the current level id (stable across transitions)
                        const key = currentLevelIdRef.current || getLevelKey(selectedNode);
                        const scrollTop = (e.currentTarget as HTMLDivElement).scrollTop;

                        // Debug: Saving scroll event
                        // console.log('📜 Saving scroll event:', {
                        //   levelKey: key,
                        //   scrollTop: scrollTop,
                        //   currentLevelId: currentLevelIdRef.current,
                        //   isRestoring: isRestoringScrollRef.current,
                        //   previousValue: scrollPositionsRef.current[key]
                        // });

                        if (key) {
                          scrollPositionsRef.current[key] = scrollTop;
                        }
                      }}
                      sx={{
                        display: 'block',
                        flexGrow: 1,
                        overflowY: 'auto',
                        p: 1,
                        minHeight: 0
                      }}
                    >
                      {showInputs && (() => {
                        // Determine the current hierarchical level index for constraint filtering
                        const findNodeByIdForLevel = (nodes: any[], id: number): any | null => {
                          for (const n of nodes) {
                            if (n.id === id) return n;
                            if (n.children) {
                              const f = findNodeByIdForLevel(n.children, id);
                              if (f) return f;
                            }
                          }
                          return null;
                        };
                        const currentLevelIndex = (() => {
                          if (selectedNode.type === 'global') return 0;
                          if (selectedNode.type === 'dataset') return 999;
                          // category: look up level from data.tree (backend sets level: 1+)
                          const cat = nodeId ? findNodeByIdForLevel(data?.tree || [], nodeId) : null;
                          return cat?.level ?? 1;
                        })();

                        const items = allVariableNames
                          .map(variableName => {
                            let variable;
                            if (selectedNode.type === 'global') {
                              variable = (data?.template_variables || []).find((v: any) => v.name === variableName);
                            } else {
                              const allVariables = getVariablesByName(variableName);
                              const selectedVariable = selectVariableForContext(allVariables, nodeId, datasetId);
                              variable = selectedVariable || allVariables[0];
                            }
                            return { name: variableName, variable };
                          })
                          // Filter out variables without components
                          .filter(({ variable }) => variable?.gui?.component_id)
                          // Enforce minLevel/maxLevel visibility: hide variables outside constraints
                          .filter(({ variable }) => {
                            const constraints = variable?.meta?.constraints || variable?.gui?.constraints;
                            if (!constraints) return true; // no constraints → visible everywhere
                            const min = typeof constraints.minLevel === 'number' ? constraints.minLevel : 0;
                            const max = typeof constraints.maxLevel === 'number' ? constraints.maxLevel : 999;
                            return currentLevelIndex >= min && currentLevelIndex <= max;
                          });

                        const groupsMap = new Map<string, any[]>();
                        for (const item of items) {
                          const g = item.variable?.gui?.group || 'Variable Tree';
                          if (!groupsMap.has(g)) groupsMap.set(g, []);
                          groupsMap.get(g)!.push(item.variable);
                        }

                        const sortedGroups = Array.from(groupsMap.entries()).sort(([ga, va], [gb, vb]) => {
                          const minA = Math.min(...va.map(v => v.gui?.order ?? Number.POSITIVE_INFINITY));
                          const minB = Math.min(...vb.map(v => v.gui?.order ?? Number.POSITIVE_INFINITY));
                          return minA - minB;
                        });

                        return (
                          <Box>
                            {sortedGroups.map(([groupName, vars]) => {
                              // Create or get ref for this group
                              if (!groupRefsRef.current.has(groupName)) {
                                groupRefsRef.current.set(groupName, React.createRef<VariableTreeGroupRendererRef>());
                              }
                              const groupRef = groupRefsRef.current.get(groupName)!;

                              return (
                                <VariableTreeGroupRenderer
                                  key={groupName}
                                  ref={groupRef}
                                  groupName={groupName}
                                  variables={vars}
                                  renderVariable={(variable) => {
                                  const state = selectedNode.type === 'global'
                                    ? getVariableState(variable.name)
                                    : getVariableStateForContext(variable.name, nodeId, datasetId);

                                  return (
                                    <VariableInputRenderer
                                      variable={variable}
                                      onChange={(variableName: string, newValue: any) => {
                                        if (selectedNode.type === 'global') {
                                          updateVariable(variableName, newValue, variable);
                                        } else {
                                          const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                          updateVariable(variableName, newValue, variable, contextId, selectedNode.type);
                                        }
                                      }}
                                      template_data={data}
                                      contextInfo={{
                                        nodeId,
                                        nodeType: selectedNode.type === 'global' ? 'category' : selectedNode.type,
                                        nodeName: selectedNode.label
                                      }}
                                      showStatusBadge={true}
                                      variableState={state}
                                      currentValue={(function() {
                                        const isResetPending = selectedNode.type === 'global'
                                          ? isVariableResetPending(variable.name)
                                          : isVariableResetPending(
                                              variable.name,
                                              selectedNode.type === 'dataset' ? datasetId : nodeId,
                                              getContextNodeType(selectedNode)
                                            );
                                        const primary = state.primaryState;
                                        const findNodeById = (nodes: any[], id: number): any | null => {
                                          for (const n of nodes) {
                                            if (n.id === id) return n;
                                            if (n.children) {
                                              const f = findNodeById(n.children, id);
                                              if (f) return f;
                                            }
                                          }
                                          return null;
                                        };
                                        const cat = nodeId ? findNodeById(data?.tree || [], nodeId) : null;
                                        const contextVar = (function() {
                                          if (selectedNode.type === 'global') {
                                            return (data?.template_variables || []).find((v: any) => v.name === variable.name);
                                          }
                                          if (selectedNode.type === 'category') {
                                            return cat?.variables?.find((v: any) => v.name === variable.name);
                                          } else if (selectedNode.type === 'dataset') {
                                            const ds = cat?.datasets?.find((d: any) => d.id === datasetId);
                                            return ds?.variables?.find((v: any) => v.name === variable.name);
                                          }
                                          return undefined;
                                        })();

                                        const inherited = getInheritedValueForContext(variable.name, nodeId, datasetId);
                                        let originalDisplayValue: any;
                                        if (isResetPending) {
                                          originalDisplayValue = inherited;
                                        } else if (state.lineageState === 'inherited') {
                                          originalDisplayValue = inherited;
                                        } else if (primary === 'active' || primary === 'overridden' || primary === 'partial') {
                                          const val = contextVar?.value ?? contextVar?.data;
                                          originalDisplayValue = (val !== undefined) ? val : inherited;
                                        } else if (primary === 'missing') {
                                          const val = contextVar?.value ?? contextVar?.data;
                                          originalDisplayValue = val !== undefined ? val : inherited;
                                        } else {
                                          originalDisplayValue = contextVar?.value ?? contextVar?.data;
                                        }
                                        return selectedNode.type === 'global'
                                          ? getVariableValue(variable.name, originalDisplayValue)
                                          : getVariableValue(
                                              variable.name,
                                              originalDisplayValue,
                                              selectedNode.type === 'dataset' ? datasetId : nodeId,
                                              getContextNodeType(selectedNode)
                                            );
                                      })()}
                                      pendingOverride={
                                        (selectedNode.type === 'global'
                                          ? isVariableChanged(variable.name)
                                          : isVariableChanged(
                                              variable.name,
                                              selectedNode.type === 'dataset' ? datasetId : nodeId,
                                              getContextNodeType(selectedNode)
                                            )) && state.lineageState === 'inherited'
                                      }
                                      pendingReset={
                                        selectedNode.type === 'global'
                                          ? isVariableResetPending(variable.name)
                                          : isVariableResetPending(
                                              variable.name,
                                              selectedNode.type === 'dataset' ? datasetId : nodeId,
                                              getContextNodeType(selectedNode)
                                            )
                                      }
                                      focusVariable={focusVariable}
                                      unfocusVariable={unfocusVariable}
                                      isVariableFocused={isVariableFocused}
                                      onMissingClick={(variableName) => openMissingPanel(variableName, state, variable, nodeId, datasetId)}
                                      onOverride={(variableName) => {
                                        const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                        const nodeType = getContextNodeType(selectedNode);
                                        const currentValue = selectedNode.type === 'global'
                                          ? getVariableValue(variableName, state.activeVariable?.variable?.data)
                                          : getVariableValue(
                                              variableName,
                                              state.activeVariable?.variable?.data,
                                              selectedNode.type === 'dataset' ? datasetId : nodeId,
                                              getContextNodeType(selectedNode)
                                            );
                                        updateVariable(variableName, currentValue, variable!, contextId, nodeType, { force: true });
                                      }}
                                      onReset={selectedNode.type === 'global' ? undefined : (variableName) => {
                                        const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                        const nodeType = selectedNode.type as 'category' | 'dataset';
                                        const inherited = getInheritedValueForContext(variableName, nodeId, datasetId);
                                        resetToInherited(variableName, contextId, nodeType, inherited);
                                      }}
                                      onGoToDefining={navigateToDefiningLevel}
                                    />
                                  );
                                }}
                              />
                              );
                            })}
                          </Box>
                        );
                      })()}
                      {!showInputs && (() => {
                        // Compute current level index for constraint filtering
                        const findNodeByIdForLevel = (nodes: any[], id: number): any | null => {
                          for (const n of nodes) {
                            if (n.id === id) return n;
                            if (n.children) {
                              const f = findNodeByIdForLevel(n.children, id);
                              if (f) return f;
                            }
                          }
                          return null;
                        };
                        const currentLevelIndex = (() => {
                          if (selectedNode.type === 'global') return 0;
                          if (selectedNode.type === 'dataset') return 999;
                          const cat = nodeId ? findNodeByIdForLevel(data?.tree || [], nodeId) : null;
                          return cat?.level ?? 1;
                        })();

                        return allVariableNames
                        .map(variableName => {
                          let variable;
                          if (selectedNode.type === 'global') {
                            // For global context, get template variables directly
                            variable = (data?.template_variables || []).find((v: any) => v.name === variableName);
                          } else {
                            // For category/dataset context, use existing logic
                            const allVariables = getVariablesByName(variableName);
                            const selectedVariable = selectVariableForContext(allVariables, nodeId, datasetId);
                            variable = selectedVariable || allVariables[0];
                          }

                          // Debug logging for GNSSES variable
                          if (variableName === 'GNSSES') {
                            console.log('GNSSES variable debug:', {
                              variableName,
                              selectedVariable: variable,
                              nodeId,
                              datasetId,
                              selectedNodeType: selectedNode.type,
                              hasGui: variable?.gui?.component_id
                            });
                          }

                          const state = selectedNode.type === 'global' ?
                            getVariableState(variableName) :
                            getVariableStateForContext(variableName, nodeId, datasetId);



                          return {
                            name: variableName,
                            state,
                            variable
                          };
                        })
                        .filter(({ variable }) => {
                          // In INPUT-KOMPONENTEN mode: only show variables with GUI components
                          // In STATUS-BADGES mode: show all variables
                          return showInputs ? variable?.gui?.component_id : true;
                        })
                        // Hide variables outside minLevel/maxLevel constraints in STATUS view
                        .filter(({ variable }) => {
                          const constraints = variable?.meta?.constraints || variable?.gui?.constraints;
                          if (!constraints) return true;
                          const min = typeof constraints.minLevel === 'number' ? constraints.minLevel : 0;
                          const max = typeof constraints.maxLevel === 'number' ? constraints.maxLevel : 999;
                          return currentLevelIndex >= min && currentLevelIndex <= max;
                        })
                        .sort((a, b) => {
                          // Sort by state priority to highlight problematic states first
                          const stateOrder: Record<string, number> = {
                            'missing': 0,
                            'partial': 1,
                            'overridden': 2,
                            'active': 3,
                            'defined-higher': 4,
                            'not-set': 5
                          };
                          const aOrder = stateOrder[a.state.primaryState] ?? 6;
                          const bOrder = stateOrder[b.state.primaryState] ?? 6;
                          if (aOrder !== bOrder) return aOrder - bOrder;
                          // If same state, sort alphabetically
                          return a.name.localeCompare(b.name);
                        })
                        .map(({ name, state, variable }, index) => {
                          if (showInputs && variable?.gui?.component_id) {
                            return (
                              <VariableInputRenderer
                                key={index}
                                variable={variable}
                                onChange={(variableName: string, newValue: any) => {
                                  // Handle different node types for variable updates
                                  if (selectedNode.type === 'global') {
                                    // Global variables are template variables (no context ID needed)
                                    updateVariable(variableName, newValue, variable);
                                  } else {
                                    // Use datasetId for datasets, nodeId for categories
                                    const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                    updateVariable(variableName, newValue, variable, contextId, selectedNode.type);
                                  }
                                }}
                                template_data={data}
                                contextInfo={{
                                  nodeId,
                                  nodeType: selectedNode.type === 'global' ? 'category' : selectedNode.type,
                                  nodeName: selectedNode.label
                                }}
                                showStatusBadge={true}
                                variableState={state}
                                currentValue={(function() {
                                  // Determine which value to display based on status and context
                                  const isResetPending = selectedNode.type === 'global'
                                    ? isVariableResetPending(variable.name)
                                    : isVariableResetPending(
                                        variable.name,
                                        selectedNode.type === 'dataset' ? datasetId : nodeId,
                                        getContextNodeType(selectedNode)
                                      );
                                  const primary = state.primaryState;
                                  // Helper: find the variable defined at the current context level
                                  const findContextVariable = () => {
                                    if (selectedNode.type === 'global') {
                                      return (data?.template_variables || []).find((v: any) => v.name === variable.name);
                                    }
                                    // Find the category node by nodeId
                                    const findNodeById = (nodes: any[], id: number): any | null => {
                                      for (const n of nodes) {
                                        if (n.id === id) return n;
                                        if (n.children) {
                                          const f = findNodeById(n.children, id);
                                          if (f) return f;
                                        }
                                      }
                                      return null;
                                    };
                                    const cat = nodeId ? findNodeById(data?.tree || [], nodeId) : null;
                                    if (selectedNode.type === 'category') {
                                      return cat?.variables?.find((v: any) => v.name === variable.name);
                                    } else if (selectedNode.type === 'dataset') {
                                      const ds = cat?.datasets?.find((d: any) => d.id === datasetId);
                                      return ds?.variables?.find((v: any) => v.name === variable.name);
                                    }
                                    return undefined;
                                  };
                                  const contextVar = findContextVariable();
                                  let originalDisplayValue: any;
                                  if (isResetPending || primary === 'defined-higher') {
                                    // Show inherited value from higher level
                                    originalDisplayValue = getInheritedValueForContext(variable.name, nodeId, datasetId);
                                  } else if (primary === 'active' || primary === 'overridden') {
                                    // Show the value actually set at the current level
                                    originalDisplayValue = contextVar?.value ?? contextVar?.data;
                                    // Fallback to inherited if not found for safety
                                    if (originalDisplayValue === undefined) {
                                      originalDisplayValue = getInheritedValueForContext(variable.name, nodeId, datasetId);
                                    }
                                  } else {
                                    // not-set or other: show undefined/inherited
                                    originalDisplayValue = contextVar?.value ?? contextVar?.data;
                                  }
                                  return selectedNode.type === 'global'
                                    ? getVariableValue(variable.name, originalDisplayValue)
                                    : getVariableValue(
                                        variable.name,
                                        originalDisplayValue,
                                        selectedNode.type === 'dataset' ? datasetId : nodeId,
                                        getContextNodeType(selectedNode)
                                      );
                                })()}
                                pendingReset={
                                  selectedNode.type === 'global'
                                    ? isVariableResetPending(variable.name)
                                    : isVariableResetPending(
                                        variable.name,
                                        selectedNode.type === 'dataset' ? datasetId : nodeId,
                                        getContextNodeType(selectedNode)
                                      )
                                }
                                pendingOverride={
                                  (selectedNode.type === 'global'
                                    ? isVariableChanged(variable.name)
                                    : isVariableChanged(
                                        variable.name,
                                        selectedNode.type === 'dataset' ? datasetId : nodeId,
                                        getContextNodeType(selectedNode)
                                      )) && state.primaryState === 'defined-higher'
                                }
                                focusVariable={focusVariable}
                                unfocusVariable={unfocusVariable}
                                isVariableFocused={isVariableFocused}
                                onMissingClick={(variableName) => openMissingPanel(variableName, state, variable, nodeId, datasetId)}
                                onReset={selectedNode.type === 'global' ? undefined : (variableName) => {
                                  const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                  const nodeType = selectedNode.type as 'category' | 'dataset';
                                  const inherited = getInheritedValueForContext(variableName, nodeId, datasetId);
                                  // LOCAL reset: mark pending reset and restore inherited visuals
                                  resetToInherited(variableName, contextId, nodeType, inherited);
                                }}
                                onOverride={(variableName) => {
                                  // Create an override at the current level
                                  const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                  const nodeType = getContextNodeType(selectedNode);
                                  // Get the current active value to use as initial override value
                                  const currentValue = selectedNode.type === 'global'
                                    ? getVariableValue(variableName, state.activeVariable?.variable?.data)
                                    : getVariableValue(
                                        variableName,
                                        state.activeVariable?.variable?.data,
                                        selectedNode.type === 'dataset' ? datasetId : nodeId,
                                        getContextNodeType(selectedNode)
                                      );
                                  updateVariable(variableName, currentValue, variable!, contextId, nodeType, { force: true });
                                }}
                                onGoToDefining={navigateToDefiningLevel}
                              />
                            );
                          } else {
                            const resetPending = selectedNode.type === 'global'
                              ? isVariableResetPending(name)
                              : isVariableResetPending(
                                  name,
                                  selectedNode.type === 'dataset' ? datasetId : nodeId,
                                  getContextNodeType(selectedNode)
                                );
                            const overridePending = selectedNode.type === 'global'
                              ? isVariableChanged(name)
                              : isVariableChanged(
                                  name,
                                  selectedNode.type === 'dataset' ? datasetId : nodeId,
                                  getContextNodeType(selectedNode)
                                );
                            const basePrimary = state.primaryState;
                            const displayPrimary = resetPending
                              ? 'defined-higher'
                              : (overridePending && state.lineageState === 'inherited'
                                  ? 'active'
                                  : basePrimary);

                            const isFocusedHere = isVariableFocused(name);

                            return (
                              <Box
                                key={index}
                                sx={{ display: 'flex', alignItems: 'center', mb: 1 }}
                                data-variable-name={name}
                              >
                                <Box
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (isFocusedHere) {
                                      unfocusVariable();
                                    } else {
                                      focusVariable(name);
                                    }
                                  }}
                                  sx={{
                                    cursor: 'pointer',
                                    display: 'inline-flex',
                                    borderRadius: 1,
                                    boxShadow: isFocusedHere
                                      ? '0 0 0 2px rgba(25, 118, 210, 0.35)'
                                      : '0 0 0 1px rgba(25, 118, 210, 0.16)',
                                    transition: 'box-shadow 200ms ease',
                                    padding: '2px 4px'
                                  }}
                                >
                                  <VariableStatusBadge
                                    variableName={name}
                                    primaryState={displayPrimary}
                                    usage={state.usage}
                                    ratioLabel={state.ratioLabel}
                                    lineageState={state.lineageState}
                                    activeSource={state.activeVariable}
                                    overriddenBy={state.overriddenBy}
                                    required={state.required}
                                    showActions={true}
                                    onGoToDefining={navigateToDefiningLevel}
                                    onOverride={(variableName) => {
                                      const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                      const nodeType = getContextNodeType(selectedNode);
                                      const currentValue = selectedNode.type === 'global'
                                        ? getVariableValue(variableName, state.activeVariable?.variable?.data)
                                        : getVariableValue(
                                            variableName,
                                            state.activeVariable?.variable?.data,
                                            selectedNode.type === 'dataset' ? datasetId : nodeId,
                                            getContextNodeType(selectedNode)
                                          );
                                      updateVariable(variableName, currentValue, variable!, contextId, nodeType, { force: true });
                                    }}
                                    currentPath={selectedNode.type === 'global' ? 'Global' :
                                      selectedNode.type === 'dataset' ? `Global → ${selectedNode.label.split(' (')[0]} → ${selectedNode.label}` :
                                      `Global → ${selectedNode.label.split(' (')[0]}`}
                                    missingDetails={state.missingDetails}
                                    descendantMissingCount={state.missingDescendants}
                                    schemaError={state.schemaError}
                                    onMissingClick={state.primaryState === 'missing'
                                      ? () => openMissingPanel(name, state, variable, nodeId, datasetId)
                                      : undefined}
                                  />
                                </Box>
                              </Box>
                            );
                          }
                        })
                      })()}
                    </Box>
                  ) : (
                    <Alert severity="info">
                      No settings available for this {selectedNode.type === 'dataset' ? 'dataset' :
                        selectedNode.type === 'global' ? 'global context' : 'category'}
                    </Alert>
                  );
                })()}
              </Box>
            ) : (
              <Alert severity="info">
                Select a node from the tree to view and configure settings
              </Alert>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Save Success Message */}
      {saveSuccess && (
        <Alert severity="success" sx={{ mt: 2 }}>
          Changes saved successfully
        </Alert>
      )}

      {/* Error Snackbar */}
      <Snackbar
        open={showErrorSnackbar}
        autoHideDuration={6000}
        onClose={() => setShowErrorSnackbar(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setShowErrorSnackbar(false)}
          severity="error"
          sx={{ width: '100%' }}
        >
          Error saving: {saveError}
        </Alert>
      </Snackbar>

      <MissingVariablePanel
        open={Boolean(missingPanelState)}
        detail={missingPanelState?.detail ?? null}
        details={missingPanelState?.details}
        selectedIndex={missingPanelState?.selectedIndex}
        onSelectDetail={handleMissingDetailSelect}
        canDefineHere={Boolean(missingPanelState?.defineHereTarget) && !missingPanelState?.schemaError}
        onDefineHere={missingPanelState?.defineHereTarget && !missingPanelState?.schemaError ? handleDefineHere : undefined}
        subtreeOption={missingPanelState?.subtreeOption && !missingPanelState?.schemaError ? missingPanelState.subtreeOption : undefined}
        onDefineSubtree={missingPanelState?.subtreeOption && !missingPanelState?.schemaError ? handleDefineForSubtree : undefined}
        schemaError={missingPanelState?.schemaError}
        summary={missingPanelState?.summary}
        originLabel={missingPanelState?.originLabel}
        onClose={closeMissingPanel}
        onNavigateToDataset={navigateToDataset}
        onDefineAtLevel={defineAtLevel}
        getVariablesByName={getVariablesByName}
        updateVariable={updateVariable}
        saveChanges={saveChanges}
        template_data={data}
        getVariableValue={(variableName: string, contextId?: number, contextType?: 'category' | 'dataset') => {
          return getVariableValue(variableName, undefined, contextId, contextType);
        }}
      />
    </Paper>
  );
};

export default VariableTreeView;
