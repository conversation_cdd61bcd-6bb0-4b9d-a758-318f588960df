import React from 'react';
import { Box } from '@mui/material';
import { getVariableStateColor, type VariableState } from '../../lib/utils/variableColors';

interface VariableFocusIndicatorProps {
  isVisible: boolean;
  primaryState: VariableState;
  size?: number;
}

export const VariableFocusIndicator: React.FC<VariableFocusIndicatorProps> = ({
  isVisible,
  primaryState,
  size = 14
}) => {
  if (!isVisible) {
    return null;
  }

  const color = getVariableStateColor(primaryState);
  const secondaryColor = primaryState === 'partial' ? getVariableStateColor('overridden') : undefined;
  const boxShadow = primaryState === 'partial'
    ? '0 0 8px #2e7d3280, 0 0 8px #f57c0080'
    : `0 0 8px ${color}80, 0 0 16px ${color}40`;

  const baseStyles: Record<string, any> = {
    width: size,
    height: size,
    borderRadius: '50%',
    display: 'inline-block',
    marginLeft: 1,
    flexShrink: 0,
    position: 'relative',
    overflow: 'hidden',
    boxShadow,
    animation: 'focusPulse 1.5s infinite',
    '@keyframes focusPulse': {
      '0%': {
        opacity: 1,
        transform: 'scale(1)',
        boxShadow
      },
      '50%': {
        opacity: 0.8,
        transform: 'scale(1.2)',
        boxShadow: primaryState === 'partial'
          ? '0 0 12px #2e7d32CC, 0 0 24px #f57c0060'
          : `0 0 12px ${color}CC, 0 0 24px ${color}60`
      },
      '100%': {
        opacity: 1,
        transform: 'scale(1)',
        boxShadow
      }
    }
  };

  if (primaryState === 'partial') {
    baseStyles.backgroundColor = 'transparent';
    baseStyles.border = '2px solid transparent';
    baseStyles['&::before'] = {
      content: '""',
      position: 'absolute',
      inset: 0,
      backgroundColor: color,
      clipPath: 'polygon(0 0, 50% 0, 50% 100%, 0 100%)'
    };
    baseStyles['&::after'] = {
      content: '""',
      position: 'absolute',
      inset: 0,
      backgroundColor: secondaryColor || color,
      clipPath: 'polygon(50% 0, 100% 0, 100% 100%, 50% 100%)'
    };
  } else {
    baseStyles.backgroundColor = color;
    baseStyles.border = `2px solid ${color}`;
  }

  return (
    <Box
      sx={baseStyles}
      title={`Focused Variable (${primaryState})`}
    />
  );
};

export default VariableFocusIndicator;