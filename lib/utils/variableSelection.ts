import { VariableWithContext } from '@/types/variable';

export function selectVariableForContext(
  variables: VariableWithContext[],
  categoryId?: number,
  datasetId?: number
): VariableWithContext | undefined {
  if (!variables || variables.length === 0) {
    return undefined;
  }

  const normalizedCategoryId = categoryId === undefined ? undefined : Number(categoryId);
  const normalizedDatasetId = datasetId === undefined ? undefined : Number(datasetId);

  if (normalizedDatasetId !== undefined) {
    const datasetVariable = variables.find(variable =>
      variable.source_level === 'Dataset' && Number(variable.dataset_id) === normalizedDatasetId
    );
    if (datasetVariable) {
      return datasetVariable;
    }
  }

  if (normalizedCategoryId !== undefined) {
    const categoryVariable = variables.find(variable =>
      variable.source_level === 'Category' && Number(variable.category_id) === normalizedCategoryId
    );
    if (categoryVariable) {
      return categoryVariable;
    }
  }

  const templateVariable = variables.find(variable => variable.source_level === 'Template');
  if (templateVariable) {
    return templateVariable;
  }

  return variables[0];
}

