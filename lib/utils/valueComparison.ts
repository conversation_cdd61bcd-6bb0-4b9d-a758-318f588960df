// Cycle-safe deep equality that is robust for typical JSON-like payloads.
// - Treats NaN === NaN
// - Treats -0 === 0
// - Compares Dates by time value
// - Compares arrays in order
// - Compares objects by keys ignoring insertion order; drops undefined props
// - Falls back to reference/primitive equality for unsupported structures
export function areValuesEqual(a: any, b: any): boolean {
  return deepEqual(a, b, new WeakMap(), new WeakMap());
}

function deepEqual(a: any, b: any, va: WeakMap<object, number>, vb: WeakMap<object, number>): boolean {
  if (a === b) {
    // Handles primitives and reference equality; includes +0 === -0 per JS semantics
    return true;
  }

  // Special-case NaN
  if (typeof a === 'number' && typeof b === 'number' && Number.isNaN(a) && Number.isNaN(b)) {
    return true;
  }

  // If one is null or types differ, not equal
  if (a === null || b === null || typeof a !== 'object' || typeof b !== 'object') {
    return false;
  }

  // Cycle handling
  if (va.has(a) && vb.has(b)) {
    return va.get(a) === vb.get(b);
  }
  const nextIndexA = ((va as any).__counter ?? 0) + 1;
  const nextIndexB = ((vb as any).__counter ?? 0) + 1;
  (va as any).__counter = nextIndexA;
  (vb as any).__counter = nextIndexB;
  va.set(a, nextIndexA);
  vb.set(b, nextIndexB);

  // Date
  if (a instanceof Date || b instanceof Date) {
    return (a instanceof Date) && (b instanceof Date) && a.getTime() === b.getTime();
  }

  // Array
  if (Array.isArray(a) || Array.isArray(b)) {
    if (!(Array.isArray(a) && Array.isArray(b))) return false;
    if (a.length !== b.length) return false;
    for (let i = 0; i < a.length; i++) {
      if (!deepEqual(a[i], b[i], va, vb)) return false;
    }
    return true;
  }

  // Typed arrays
  if (ArrayBuffer.isView(a) || ArrayBuffer.isView(b)) {
    if (!(ArrayBuffer.isView(a) && ArrayBuffer.isView(b))) return false;
    // Compare constructor and byte length
    if (a.constructor !== b.constructor || a.byteLength !== b.byteLength) return false;
    const ua = new Uint8Array((a as any).buffer, (a as any).byteOffset, (a as any).byteLength);
    const ub = new Uint8Array((b as any).buffer, (b as any).byteOffset, (b as any).byteLength);
    if (ua.length !== ub.length) return false;
    for (let i = 0; i < ua.length; i++) if (ua[i] !== ub[i]) return false;
    return true;
  }

  // Plain objects: compare keys ignoring order; drop undefined
  const aIsPlain = isPlainObject(a);
  const bIsPlain = isPlainObject(b);
  if (aIsPlain && bIsPlain) {
    const aKeys = Object.keys(a).filter(k => a[k] !== undefined).sort();
    const bKeys = Object.keys(b).filter(k => b[k] !== undefined).sort();
    if (aKeys.length !== bKeys.length) return false;
    for (let i = 0; i < aKeys.length; i++) {
      if (aKeys[i] !== bKeys[i]) return false;
      const k = aKeys[i];
      if (!deepEqual(a[k], b[k], va, vb)) return false;
    }
    return true;
  }

  // Fallback: different object types → not equal
  return false;
}

function isPlainObject(obj: any): obj is Record<string, any> {
  if (obj === null || typeof obj !== 'object') return false;
  const proto = Object.getPrototypeOf(obj);
  return proto === Object.prototype || proto === null;
}
