import { VariableWithContext, VariableSaveContext, VariableContextState } from '@/types/variable';

/**
 * Determines where a variable should be saved based on its context and current state
 */
export function determineVariableSaveContext(
  variableWithContext: VariableWithContext,
  nodeId?: number,
  nodeType?: 'category' | 'dataset'
): VariableSaveContext {
  // If we have a specific node context (nodeId and nodeType provided),
  // this means we want to create an override at that level
  if (nodeId && nodeType) {
    return {
      saveLevel: nodeType,
      targetId: nodeId,
      targetType: nodeType
    };
  }

  // If no specific context is provided, determine based on variable's current state
  // If the variable is currently active at template level, save to template
  if (variableWithContext.source_level === 'Template' && variableWithContext.is_active) {
    return {
      saveLevel: 'template'
    };
  }

  // If the variable is active at category level, save to category
  if (variableWithContext.source_level === 'Category' && variableWithContext.is_active) {
    // We need the nodeId to save to category, but if not provided, fall back to template
    return {
      saveLevel: 'template'
    };
  }

  // If the variable is active at dataset level, save to dataset
  if (variableWithContext.source_level === 'Dataset' && variableWithContext.is_active) {
    // We need the nodeId to save to dataset, but if not provided, fall back to template
    return {
      saveLevel: 'template'
    };
  }

  // Default to template level
  return {
    saveLevel: 'template'
  };
}

export function isDatasetReady(states: VariableContextState[]): boolean {
  return states.every(state => state.primaryState !== 'missing' || state.schemaError);
}
