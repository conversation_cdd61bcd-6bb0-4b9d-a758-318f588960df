/**
 * Variable state color definitions
 * Used by VariableStatusBadge and variable focus indicators
 */

export type VariableState = 'active' | 'overridden' | 'not-set' | 'defined-higher' | 'partial' | 'missing';

/**
 * Get the color for a variable state
 * These colors match the existing VariableStatusBadge implementation
 */
export const getVariableStateColor = (state: VariableState): string => {
  switch (state) {
    case 'active':
      return '#2e7d32'; // Darker Green for better visibility
    case 'partial':
      return '#2e7d32'; // Base green used for gradients
    case 'overridden':
      return '#f57c00'; // Darker Orange for better contrast
    case 'missing':
      return '#d32f2f'; // Red for missing required
    case 'defined-higher':
      return '#616161'; // Darker Gray for better visibility
    case 'not-set':
      return '#757575';
    default:
      return '#757575';
  }
};

export const getVariableStateGradient = (state: VariableState): string | undefined => {
  if (state === 'partial') {
    return 'linear-gradient(90deg, #2e7d32 0%, #2e7d32 50%, #f57c00 50%, #f57c00 100%)';
  }
  return undefined;
};

/**
 * Get the label for a variable state
 */
export const getVariableStateLabel = (state: VariableState): string => {
  switch (state) {
    case 'active':
      return 'Active';
    case 'overridden':
      return 'Overridden';
    case 'partial':
      return 'Partially used';
    case 'missing':
      return 'Missing required';
    case 'defined-higher':
      return 'Higher level';
    case 'not-set':
    default:
      return 'Not set';
  }
};