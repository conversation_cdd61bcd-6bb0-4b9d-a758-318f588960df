import { useState, useEffect, useCallback, useMemo } from 'react';
import {
  VariableTreeResponse,
  VariableWithContext,
  VariableTreeNode,
  VariableContextState,
  VariableUsageCounts,
  VariableSourceDetail,
  VariableUsageStatus,
  VariableLineageState,
  MissingVariableDetail,
  VariableLevelOption,
  VariableLevelNodeType,
  MissingNodeSummary
} from '@/types/variable';

export interface UseVariableTreeOptions {
  templateId: number;
  enabled?: boolean;
  refetchInterval?: number;
}

export interface UseVariableTreeReturn {
  data: VariableTreeResponse | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  getVariablesByName: (variableName: string) => VariableWithContext[];
  getVariableState: (variableName: string) => VariableContextState;
  getVariableStateForContext: (
    variableName: string,
    contextNodeId?: number,
    contextDatasetId?: number
  ) => VariableContextState;
  getInheritedValueForContext: (
    variableName: string,
    contextNodeId?: number,
    contextDatasetId?: number
  ) => any | undefined;
  focusedVariable: string | null;
  focusVariable: (variableName: string) => void;
  unfocusVariable: () => void;
  isVariableFocused: (variableName: string) => boolean;
  isDatasetReady: (datasetId: number) => boolean;
  getNodeMissingSummary: (nodeKey: string) => MissingNodeSummary;
}

type NodeKey = string;

interface VariableMetaInfo {
  required: boolean;
  constraints: {
    minLevel: number;
    maxLevel: number;
  };
}

interface VariableSourceSnapshot {
  sourceLevel: 'Template' | 'Category' | 'Dataset' | 'Undefined';
  levelNumber: number;
  nodeId?: number;
  datasetId?: number;
  path: string;
  variable: VariableWithContext | null;
  hasValue: boolean;
}

interface CategoryPathNode {
  id: number;
  name: string;
  level: number;
  key: NodeKey;
}

interface DatasetInfo {
  id: number;
  name: string;
  key: NodeKey;
  categoryPath: CategoryPathNode[];
  pathLabels: string[];
  ancestorCategoryIds: number[];
}

interface HierarchyInfo {
  nodeDatasets: Map<NodeKey, number[]>;
  nodeLevels: Map<NodeKey, number>;
  nodePaths: Map<NodeKey, string[]>;
  datasetInfos: Map<number, DatasetInfo>;
  datasetSourcesByVariable: Map<string, Map<number, VariableSourceSnapshot>>;
  datasetApplicableLevelsByVariable: Map<string, Map<number, number[]>>;
  missingAssignments: Map<string, Map<NodeKey, number>>;
  missingDescendantAssignments: Map<string, Map<NodeKey, number>>;
  missingDetailsByNode: Map<NodeKey, Map<string, MissingVariableDetail>>;
  datasetMissingVariables: Map<number, Set<string>>;
  datasetReadyMap: Map<number, boolean>;
  schemaErrorDetailsByNode: Map<NodeKey, Map<string, MissingVariableDetail>>;
  variableDefinitionsByNode: Map<NodeKey, Map<string, VariableWithContext>>;
  variableMetaByName: Map<string, VariableMetaInfo>;
  templateVariableMap: Map<string, VariableWithContext>;
  categoryAncestorMap: Map<number, number[]>;
  aggregatedMissingDetailsByNode: Map<NodeKey, MissingVariableDetail[]>;
  nodeMissingDatasetCounts: Map<NodeKey, { missing: number; total: number }>;
}

const TEMPLATE_KEY = 'global-root';

const normalizeConstraints = (variable?: VariableWithContext): { minLevel: number; maxLevel: number } => {
  const constraints = variable?.meta?.constraints || variable?.gui?.constraints || {};
  const minLevel = typeof constraints.minLevel === 'number' ? constraints.minLevel : 0;
  const maxLevel = typeof constraints.maxLevel === 'number' ? constraints.maxLevel : 999;
  return { minLevel, maxLevel };
};

const extractRequired = (variable?: VariableWithContext): boolean => {
  return Boolean(variable?.meta?.required ?? variable?.gui?.required);
};

const isValueProvided = (variable?: VariableWithContext | null): boolean => {
  if (!variable) return false;
  if (variable.unset_required) return false;
  const value = variable.value !== undefined ? variable.value : variable.data;
  return value !== null && value !== undefined;
};

const buildEmptyState = (): VariableContextState => ({
  primaryState: 'not-set',
  usage: { used: 0, overridden: 0, missing: 0, total: 0 },
  ratioLabel: '0/0',
  lineageState: 'undefined',
  activeVariable: null,
  overriddenBy: [],
  missingCount: 0,
  totalRelevantDatasets: 0,
  definedHere: false,
  required: false,
  missingDetails: null,
  missingDescendants: 0,
  schemaError: false
});

const buildHierarchyInfo = (data: VariableTreeResponse | null): HierarchyInfo | null => {
  if (!data) return null;

  const nodeDatasets = new Map<NodeKey, number[]>();
  const nodeLevels = new Map<NodeKey, number>();
  const nodePaths = new Map<NodeKey, string[]>();
  const datasetInfos = new Map<number, DatasetInfo>();
  const datasetSourcesByVariable = new Map<string, Map<number, VariableSourceSnapshot>>();
  const datasetApplicableLevelsByVariable = new Map<string, Map<number, number[]>>();
  const missingAssignments = new Map<string, Map<NodeKey, number>>();
  const missingDescendantAssignments = new Map<string, Map<NodeKey, number>>();
  const missingDetailsByNode = new Map<NodeKey, Map<string, MissingVariableDetail>>();
  const datasetMissingVariables = new Map<number, Set<string>>();
  const datasetReadyMap = new Map<number, boolean>();
  const schemaErrorDetailsByNode = new Map<NodeKey, Map<string, MissingVariableDetail>>();
  const variableDefinitionsByNode = new Map<NodeKey, Map<string, VariableWithContext>>();
  const variableMetaByName = new Map<string, VariableMetaInfo>();
  const templateVariableMap = new Map<string, VariableWithContext>();
  const categoryAncestorMap = new Map<number, number[]>();
  const aggregatedMissingDetailsByNode = new Map<NodeKey, MissingVariableDetail[]>();
  const nodeMissingDatasetCounts = new Map<NodeKey, { missing: number; total: number }>();

  const loggedSchemaErrors = new Set<string>();

  nodeLevels.set(TEMPLATE_KEY, 0);
  nodePaths.set(TEMPLATE_KEY, ['Global']);

  const recordMeta = (variable: VariableWithContext | undefined, force = false) => {
    if (!variable?.name) return;
    const current = variableMetaByName.get(variable.name);
    const constraints = normalizeConstraints(variable);
    const required = extractRequired(variable);

    if (!current || force) {
      variableMetaByName.set(variable.name, {
        required,
        constraints
      });
      return;
    }

    const merged: VariableMetaInfo = {
      required: current.required || required,
      constraints: {
        minLevel: Math.min(current.constraints.minLevel, constraints.minLevel),
        maxLevel: Math.max(current.constraints.maxLevel, constraints.maxLevel)
      }
    };
    variableMetaByName.set(variable.name, merged);
  };

  const templateVariables = Array.isArray(data.template_variables) ? data.template_variables : [];
  templateVariables.forEach(variable => {
    templateVariableMap.set(variable.name, variable);
    recordMeta(variable, true);
  });
  variableDefinitionsByNode.set(TEMPLATE_KEY, templateVariableMap);

  const traverseCategory = (node: VariableTreeNode, path: CategoryPathNode[]): number[] => {
    const level = typeof node.level === 'number' ? node.level : path.length + 1;
    const key: NodeKey = `category-${node.id}`;
    const categoryNode: CategoryPathNode = { id: node.id, name: node.name, level, key };
    const currentPath = [...path, categoryNode];

    categoryAncestorMap.set(node.id, path.map(p => p.id));
    nodeLevels.set(key, level);
    nodePaths.set(key, currentPath.map(c => c.name));

    const variableMap = new Map<string, VariableWithContext>();
    (Array.isArray(node.variables) ? node.variables : []).forEach(variable => {
      variableMap.set(variable.name, variable);
      recordMeta(variable);
    });
    variableDefinitionsByNode.set(key, variableMap);

    const datasetIds: number[] = [];

    (Array.isArray(node.datasets) ? node.datasets : []).forEach(dataset => {
      const datasetKey: NodeKey = `dataset-${dataset.id}`;
      const dsVarMap = new Map<string, VariableWithContext>();
      (Array.isArray(dataset.variables) ? dataset.variables : []).forEach(variable => {
        dsVarMap.set(variable.name, variable);
        recordMeta(variable);
      });
      variableDefinitionsByNode.set(datasetKey, dsVarMap);
      nodeLevels.set(datasetKey, 999);
      const pathLabels = [...currentPath.map(c => c.name), dataset.name];
      nodePaths.set(datasetKey, pathLabels);
      nodeDatasets.set(datasetKey, [dataset.id]);

      datasetInfos.set(dataset.id, {
        id: dataset.id,
        name: dataset.name,
        key: datasetKey,
        categoryPath: currentPath,
        pathLabels,
        ancestorCategoryIds: currentPath.map(c => c.id)
      });

      datasetIds.push(dataset.id);
    });

    (Array.isArray(node.children) ? node.children : []).forEach(child => {
      const childDatasetIds = traverseCategory(child, currentPath);
      datasetIds.push(...childDatasetIds);
    });

    nodeDatasets.set(key, datasetIds);
    return datasetIds;
  };

  const allDatasetIds: number[] = [];
  (Array.isArray(data.tree) ? data.tree : []).forEach(rootNode => {
    const ids = traverseCategory(rootNode, []);
    allDatasetIds.push(...ids);
  });
  nodeDatasets.set(TEMPLATE_KEY, allDatasetIds);

  const ensureInnerMap = <K, T>(map: Map<string, Map<K, T>>, key: string): Map<K, T> => {
    let inner = map.get(key);
    if (!inner) {
      inner = new Map<K, T>();
      map.set(key, inner);
    }
    return inner;
  };

  const ensureNodeDetailMap = (map: Map<NodeKey, Map<string, MissingVariableDetail>>, key: NodeKey) => {
    let inner = map.get(key);
    if (!inner) {
      inner = new Map<string, MissingVariableDetail>();
      map.set(key, inner);
    }
    return inner;
  };

  const getLevelLabelForDataset = (level: number, datasetInfo: DatasetInfo): string => {
    if (level === 0) return 'Template';
    if (level === 999) return 'Dataset';
    const index = datasetInfo.categoryPath.findIndex(cat => cat.level === level);
    if (index >= 0) {
      if (index === 0) return 'Campaign';
      if (index === 1) return 'Subcampaign';
      return datasetInfo.categoryPath[index]?.name ?? `Category Level ${level}`;
    }
    return `Level ${level}`;
  };

  const resolveNodeForLevel = (
    level: number,
    datasetInfo: DatasetInfo
  ): { nodeKey: NodeKey; nodeId?: number; nodeType: VariableLevelNodeType } => {
    if (level === 0) {
      return { nodeKey: TEMPLATE_KEY, nodeType: 'template' };
    }
    if (level === 999) {
      return { nodeKey: datasetInfo.key, nodeType: 'dataset', nodeId: datasetInfo.id };
    }
    const category = datasetInfo.categoryPath.find(cat => cat.level === level);
    if (category) {
      return { nodeKey: category.key, nodeId: category.id, nodeType: 'category' };
    }
    return { nodeKey: TEMPLATE_KEY, nodeType: 'template' };
  };

  datasetInfos.forEach(info => {
    const datasetKey = info.key;
    const datasetVarMap = variableDefinitionsByNode.get(datasetKey) ?? new Map();
    const categories = info.categoryPath;
    const levelSequence = [0, ...categories.map(c => c.level), 999];

    if (!datasetReadyMap.has(info.id)) {
      datasetReadyMap.set(info.id, true);
    }

    const variableNames = new Set<string>();
    templateVariableMap.forEach((_, name) => variableNames.add(name));
    datasetVarMap.forEach((_, name) => variableNames.add(name));
    categories.forEach(cat => {
      const map = variableDefinitionsByNode.get(cat.key);
      map?.forEach((_, name) => variableNames.add(name));
    });

    variableNames.forEach(variableName => {
      const meta = variableMetaByName.get(variableName) ?? { required: false, constraints: { minLevel: 0, maxLevel: 999 } };
      const applicableLevels = levelSequence.filter(level => level >= meta.constraints.minLevel && level <= meta.constraints.maxLevel);

      if (applicableLevels.length === 0) {
        if (meta.required) {
          const errorKey = `${variableName}-${info.id}`;
          if (!loggedSchemaErrors.has(errorKey)) {
            console.error(
              `VariableTree: Required variable "${variableName}" cannot be set for dataset "${info.name}" (ID ${info.id}) due to min/max constraints.`
            );
            loggedSchemaErrors.add(errorKey);
          }
          const schemaDetail: MissingVariableDetail = {
            variableName,
            datasetId: info.id,
            datasetName: info.name,
            targetLevel: -1,
            targetNodeKey: datasetKey,
            allowedLevels: [],
            reason: 'Required variable cannot be set because of the min/max settings (schema configuration).',
            schemaError: true
          };
          const schemaMap = ensureNodeDetailMap(schemaErrorDetailsByNode, datasetKey);
          schemaMap.set(variableName, schemaDetail);
        }
        return;
      }

      const allowedLevels = new Set(applicableLevels);
      const datasetVariable = datasetVarMap.get(variableName);
      const templateVariable = templateVariableMap.get(variableName);

      let hasValueAnywhere = false;
      let source: VariableSourceSnapshot = {
        sourceLevel: 'Undefined',
        levelNumber: -1,
        nodeId: undefined,
        datasetId: undefined,
        path: '',
        variable: null,
        hasValue: false
      };

      if (datasetVariable && allowedLevels.has(999) && isValueProvided(datasetVariable)) {
        source = {
          sourceLevel: 'Dataset',
          levelNumber: 999,
          nodeId: categories.length ? categories[categories.length - 1].id : undefined,
          datasetId: info.id,
          path: info.pathLabels.join(' → '),
          variable: datasetVariable,
          hasValue: true
        };
        hasValueAnywhere = true;
      } else {
        let foundCategoryVariable: VariableWithContext | undefined;
        let foundCategoryIndex = -1;
        for (let index = categories.length - 1; index >= 0; index--) {
          const category = categories[index];
          if (!allowedLevels.has(category.level)) {
            continue;
          }
          const catVariable = variableDefinitionsByNode.get(category.key)?.get(variableName);
          if (isValueProvided(catVariable)) {
            foundCategoryVariable = catVariable;
            foundCategoryIndex = index;
            break;
          }
        }

        if (foundCategoryVariable && foundCategoryIndex >= 0) {
          const category = categories[foundCategoryIndex];
          source = {
            sourceLevel: 'Category',
            levelNumber: category.level,
            nodeId: category.id,
            datasetId: undefined,
            path: categories.slice(0, foundCategoryIndex + 1).map(c => c.name).join(' → '),
            variable: foundCategoryVariable,
            hasValue: true
          };
          hasValueAnywhere = true;
        } else if (allowedLevels.has(0) && isValueProvided(templateVariable)) {
          source = {
            sourceLevel: 'Template',
            levelNumber: 0,
            nodeId: undefined,
            datasetId: undefined,
            path: 'Global',
            variable: templateVariable ?? null,
            hasValue: true
          };
          hasValueAnywhere = true;
        }
      }

      if (!hasValueAnywhere) {
        if (datasetVariable && allowedLevels.has(999)) {
          source = {
            sourceLevel: 'Dataset',
            levelNumber: 999,
            nodeId: categories.length ? categories[categories.length - 1].id : undefined,
            datasetId: info.id,
            path: info.pathLabels.join(' → '),
            variable: datasetVariable,
            hasValue: false
          };
        } else if (allowedLevels.has(0) && templateVariable) {
          source = {
            sourceLevel: 'Template',
            levelNumber: 0,
            nodeId: undefined,
            datasetId: undefined,
            path: 'Global',
            variable: templateVariable,
            hasValue: isValueProvided(templateVariable)
          };
        } else if (categories.length > 0) {
          const lastCategory = categories[categories.length - 1];
          const catVariable = variableDefinitionsByNode.get(lastCategory.key)?.get(variableName) ?? null;
          source = {
            sourceLevel: 'Category',
            levelNumber: lastCategory.level,
            nodeId: lastCategory.id,
            datasetId: undefined,
            path: categories.map(c => c.name).join(' → '),
            variable: catVariable,
            hasValue: isValueProvided(catVariable ?? undefined)
          };
        }
      }

      const sourceMap = ensureInnerMap(datasetSourcesByVariable, variableName);
      sourceMap.set(info.id, source);

      const levelMap = ensureInnerMap(datasetApplicableLevelsByVariable, variableName);
      levelMap.set(info.id, applicableLevels);

      if (meta.required && !hasValueAnywhere) {
        const targetLevel = Math.max(...applicableLevels);
        const resolvedTarget = resolveNodeForLevel(targetLevel, info);
        const missingMap = ensureInnerMap(missingAssignments, variableName);
        missingMap.set(resolvedTarget.nodeKey, (missingMap.get(resolvedTarget.nodeKey) ?? 0) + 1);

        const descendantMap = ensureInnerMap(missingDescendantAssignments, variableName);
        const descendantKeys: NodeKey[] = [info.key, ...categories.map(cat => cat.key), TEMPLATE_KEY];
        if (!descendantKeys.includes(resolvedTarget.nodeKey)) {
          descendantKeys.push(resolvedTarget.nodeKey);
        }
        descendantKeys.forEach(nodeKey => {
          descendantMap.set(nodeKey, (descendantMap.get(nodeKey) ?? 0) + 1);
        });

        const allowedOptions: VariableLevelOption[] = applicableLevels.map(level => {
          const resolved = resolveNodeForLevel(level, info);
          return {
            level,
            label: getLevelLabelForDataset(level, info),
            nodeKey: resolved.nodeKey,
            nodeType: resolved.nodeType,
            nodeId: resolved.nodeId,
            datasetId: resolved.nodeType === 'dataset' ? info.id : undefined
          };
        });

        const detail: MissingVariableDetail = {
          variableName,
          datasetId: info.id,
          datasetName: info.name,
          targetLevel,
          targetNodeKey: resolvedTarget.nodeKey,
          allowedLevels: allowedOptions,
          reason: 'Required variable is not defined anywhere.',
          schemaError: false
        };
        const detailMap = ensureNodeDetailMap(missingDetailsByNode, resolvedTarget.nodeKey);
        detailMap.set(variableName, detail);

        let datasetMissingSet = datasetMissingVariables.get(info.id);
        if (!datasetMissingSet) {
          datasetMissingSet = new Set<string>();
          datasetMissingVariables.set(info.id, datasetMissingSet);
        }
        datasetMissingSet.add(variableName);

        datasetReadyMap.set(info.id, false);
      }
    });
  });

  nodeDatasets.forEach((datasetIds, nodeKey) => {
    const totalDatasets = datasetIds.length;
    let missingDatasetCount = 0;
    datasetIds.forEach(datasetId => {
      const missingSet = datasetMissingVariables.get(datasetId);
      if (missingSet && missingSet.size > 0) {
        missingDatasetCount += 1;
      }
    });

    const detailAccumulator: MissingVariableDetail[] = [];
    const seenDetails = new Set<string>();

    const includeDetailsFromNode = (targetNodeKey: NodeKey) => {
      const detailMap = missingDetailsByNode.get(targetNodeKey);
      if (!detailMap) {
        return;
      }

      detailMap.forEach(detail => {
        const dedupeKey = `${detail.variableName}::${detail.datasetId ?? targetNodeKey}`;
        if (seenDetails.has(dedupeKey)) {
          return;
        }
        seenDetails.add(dedupeKey);

        const descendantMap = missingDescendantAssignments.get(detail.variableName);
        const affectedDescendants = descendantMap?.get(nodeKey);

        detailAccumulator.push({
          ...detail,
          affectedDescendantCount: affectedDescendants ?? detail.affectedDescendantCount ?? (detail.datasetId ? 1 : 0),
          totalDescendantDatasets: totalDatasets
        });
      });
    };

    includeDetailsFromNode(nodeKey);

    datasetIds.forEach(datasetId => {
      const datasetKey: NodeKey = `dataset-${datasetId}`;
      if (datasetKey === nodeKey) {
        return;
      }
      includeDetailsFromNode(datasetKey);
    });

    aggregatedMissingDetailsByNode.set(nodeKey, detailAccumulator);
    nodeMissingDatasetCounts.set(nodeKey, { missing: missingDatasetCount, total: totalDatasets });
  });

  return {
    nodeDatasets,
    nodeLevels,
    nodePaths,
    datasetInfos,
    datasetSourcesByVariable,
    datasetApplicableLevelsByVariable,
    missingAssignments,
    missingDescendantAssignments,
    missingDetailsByNode,
    datasetMissingVariables,
    datasetReadyMap,
    schemaErrorDetailsByNode,
    variableDefinitionsByNode,
    variableMetaByName,
    templateVariableMap,
    categoryAncestorMap,
    aggregatedMissingDetailsByNode,
    nodeMissingDatasetCounts
  };
};

export function useVariableTree({
  templateId,
  enabled = true,
  refetchInterval
}: UseVariableTreeOptions): UseVariableTreeReturn {
  const [data, setData] = useState<VariableTreeResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [focusedVariable, setFocusedVariable] = useState<string | null>(null);

  const fetchVariableTree = useCallback(async () => {
    if (!enabled || !templateId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/variable-tree?templateId=${templateId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch variable tree');
      }

      const result: VariableTreeResponse = await response.json();
      setData(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching variable tree:', err);
    } finally {
      setLoading(false);
    }
  }, [templateId, enabled]);

  useEffect(() => {
    fetchVariableTree();
  }, [fetchVariableTree]);

  useEffect(() => {
    if (!refetchInterval || !enabled) return;

    const interval = setInterval(fetchVariableTree, refetchInterval);
    return () => clearInterval(interval);
  }, [fetchVariableTree, refetchInterval, enabled]);

  const getVariablesByName = useCallback((variableName: string): VariableWithContext[] => {
    if (!data) return [];

    const variables: VariableWithContext[] = [];

    const templateVariables = Array.isArray(data.template_variables) ? data.template_variables : [];
    const templateVar = templateVariables.find(v => v.name === variableName);
    if (templateVar) {
      variables.push(templateVar);
    }

    const searchNode = (node: VariableTreeNode) => {
      const nodeVariables = Array.isArray(node.variables) ? node.variables : [];
      const nodeVar = nodeVariables.find(v => v.name === variableName);
      if (nodeVar) {
        variables.push({
          ...nodeVar,
          source_level: 'Category',
          is_active: nodeVar.is_active ?? true,
          is_overridden: nodeVar.is_overridden ?? false,
          value: nodeVar.value ?? nodeVar.data,
          category_id: node.id
        });
      }

      if (node.datasets) {
        node.datasets.forEach(dataset => {
          const datasetVariables = Array.isArray(dataset.variables) ? dataset.variables : [];
          const datasetVar = datasetVariables.find(v => v.name === variableName);
          if (datasetVar) {
            variables.push({
              ...datasetVar,
              source_level: 'Dataset',
              is_active: datasetVar.is_active ?? true,
              is_overridden: datasetVar.is_overridden ?? false,
              value: datasetVar.value ?? datasetVar.data,
              category_id: node.id,
              dataset_id: dataset.id
            });
          }
        });
      }

      if (node.children) {
        node.children.forEach(searchNode);
      }
    };

    const treeNodes = Array.isArray(data.tree) ? data.tree : [];
    treeNodes.forEach(searchNode);
    return variables;
  }, [data]);

  const hierarchyInfo = useMemo(() => buildHierarchyInfo(data), [data]);

  const computeStateForNode = useCallback((
    variableName: string,
    nodeKey: NodeKey,
    contextNodeId?: number,
    contextDatasetId?: number
  ): VariableContextState => {
    if (!hierarchyInfo) {
      return buildEmptyState();
    }

    const {
      nodeDatasets,
      nodeLevels,
      datasetSourcesByVariable,
      datasetApplicableLevelsByVariable,
      missingAssignments,
      missingDescendantAssignments,
      missingDetailsByNode,
      schemaErrorDetailsByNode,
      variableDefinitionsByNode,
      datasetInfos,
      nodePaths,
      variableMetaByName,
      categoryAncestorMap
    } = hierarchyInfo;

    if (!nodeLevels.has(nodeKey)) {
      return buildEmptyState();
    }

    const nodeLevel = nodeLevels.get(nodeKey)!;
    const datasetIds = nodeDatasets.get(nodeKey) ?? [];
    const sourcesMap = datasetSourcesByVariable.get(variableName) ?? new Map();
    const levelMap = datasetApplicableLevelsByVariable.get(variableName) ?? new Map();
    const missingMap = missingAssignments.get(variableName) ?? new Map();
    const descendantMissingMap = missingDescendantAssignments.get(variableName) ?? new Map();
    const missingDetailMap = missingDetailsByNode.get(nodeKey);
    const schemaErrorDetailMap = schemaErrorDetailsByNode.get(nodeKey);
    const definedHere = variableDefinitionsByNode.get(nodeKey)?.has(variableName) ?? false;
    const definedVariable = definedHere ? variableDefinitionsByNode.get(nodeKey)!.get(variableName)! : undefined;
    const meta = variableMetaByName.get(variableName) ?? { required: false, constraints: { minLevel: 0, maxLevel: 999 } };

    const relevantDatasetIds = datasetIds.filter(id => {
      const levels = levelMap.get(id);
      return levels ? levels.includes(nodeLevel) : false;
    });
    const totalRelevant = relevantDatasetIds.length;

    let usedCount = 0;
    const overrideDatasetIds: number[] = [];
    const overridePaths = new Set<string>();

    relevantDatasetIds.forEach(id => {
      const source = sourcesMap.get(id);
      if (!source) return;

      if (source.hasValue) {
        if (nodeLevel === 0 && source.sourceLevel === 'Template') {
          usedCount += 1;
        } else if (nodeLevel === 999 && source.sourceLevel === 'Dataset' && source.datasetId === contextDatasetId) {
          usedCount += 1;
        } else if (nodeLevel !== 0 && nodeLevel !== 999 && source.sourceLevel === 'Category' && source.nodeId === contextNodeId) {
          usedCount += 1;
        } else {
          const datasetInfo = datasetInfos.get(id);
          if (nodeLevel === 0 && (source.sourceLevel === 'Category' || source.sourceLevel === 'Dataset')) {
            overrideDatasetIds.push(id);
            if (source.path) overridePaths.add(source.path);
          } else if (nodeLevel !== 0 && nodeLevel !== 999) {
            if (source.sourceLevel === 'Dataset') {
              if (datasetInfo?.ancestorCategoryIds.includes(contextNodeId!)) {
                overrideDatasetIds.push(id);
                if (source.path) overridePaths.add(source.path);
              }
            } else if (source.sourceLevel === 'Category' && source.nodeId !== contextNodeId) {
              const ancestors = source.nodeId != null ? categoryAncestorMap.get(source.nodeId) ?? [] : [];
              if (ancestors.includes(contextNodeId!)) {
                overrideDatasetIds.push(id);
                if (source.path) overridePaths.add(source.path);
              }
            }
          }
        }
      }
    });

    const overridesCount = overrideDatasetIds.length;
    const missingCount = missingMap.get(nodeKey) ?? 0;
    const descendantMissingCount = descendantMissingMap.get(nodeKey) ?? 0;
    const missingDetails = missingDetailMap?.get(variableName) ?? null;
    const schemaErrorDetail = schemaErrorDetailMap?.get(variableName) ?? null;
    const usage: VariableUsageCounts = {
      used: usedCount,
      overridden: overridesCount,
      missing: missingCount,
      total: totalRelevant
    };

    let primaryState: VariableContextState['primaryState'];
    if (missingCount > 0 && (!definedHere || usedCount === 0)) {
      primaryState = 'missing';
    } else if (!definedHere) {
      const hasValue = relevantDatasetIds.some(id => {
        const source = sourcesMap.get(id);
        return source?.hasValue;
      });
      primaryState = hasValue ? 'defined-higher' : 'not-set';
    } else if (totalRelevant === 0) {
      primaryState = 'active';
    } else if (usedCount === totalRelevant) {
      primaryState = 'active';
    } else if (usedCount === 0) {
      primaryState = overridesCount > 0 ? 'overridden' : (missingCount > 0 ? 'missing' : 'overridden');
    } else {
      primaryState = 'partial';
    }

    const ratioLabel = totalRelevant > 0 ? `${usedCount}/${totalRelevant}` : '0/0';

    let lineageState: VariableLineageState;
    if (definedHere) {
      lineageState = 'defined-here';
    } else if (primaryState === 'defined-higher') {
      lineageState = 'inherited';
    } else {
      lineageState = 'undefined';
    }

    let activeVariable: VariableSourceDetail | null = null;
    if (contextDatasetId !== undefined) {
      const source = sourcesMap.get(contextDatasetId);
      if (source) {
        activeVariable = {
          variable: source.variable,
          level: source.levelNumber,
          path: source.path,
          nodeId: source.nodeId,
          datasetId: source.datasetId,
          hasValue: source.hasValue
        };
      }
    } else if (definedHere && definedVariable) {
      const path = nodePaths.get(nodeKey)?.join(' → ') ?? '';
      activeVariable = {
        variable: definedVariable,
        level: nodeLevel,
        path,
        nodeId: contextNodeId,
        datasetId: undefined,
        hasValue: isValueProvided(definedVariable)
      };
    } else {
      const firstUsedDataset = relevantDatasetIds.find(id => {
        const source = sourcesMap.get(id);
        return source?.hasValue;
      });
      if (firstUsedDataset !== undefined) {
        const source = sourcesMap.get(firstUsedDataset)!;
        activeVariable = {
          variable: source.variable,
          level: source.levelNumber,
          path: source.path,
          nodeId: source.nodeId,
          datasetId: source.datasetId,
          hasValue: source.hasValue
        };
      } else {
        const fallbackSource = sourcesMap.values().next().value as VariableSourceSnapshot | undefined;
        if (fallbackSource) {
          activeVariable = {
            variable: fallbackSource.variable,
            level: fallbackSource.levelNumber,
            path: fallbackSource.path,
            nodeId: fallbackSource.nodeId,
            datasetId: fallbackSource.datasetId,
            hasValue: fallbackSource.hasValue
          };
        }
      }
    }

    return {
      primaryState,
      usage,
      ratioLabel,
      lineageState,
      activeVariable,
      overriddenBy: Array.from(overridePaths),
      missingCount,
      totalRelevantDatasets: totalRelevant,
      definedHere,
      required: meta.required,
      missingDetails: missingDetails ?? schemaErrorDetail ?? null,
      missingDescendants: descendantMissingCount,
      schemaError: Boolean(schemaErrorDetail)
    };
  }, [hierarchyInfo]);

  const getVariableState = useCallback((variableName: string) => {
    if (!hierarchyInfo) return buildEmptyState();
    return computeStateForNode(variableName, TEMPLATE_KEY);
  }, [hierarchyInfo, computeStateForNode]);

  const getVariableStateForContext = useCallback((variableName: string, contextNodeId?: number, contextDatasetId?: number) => {
    if (!hierarchyInfo) return buildEmptyState();

    if (contextNodeId === undefined && contextDatasetId === undefined) {
      return computeStateForNode(variableName, TEMPLATE_KEY);
    }

    let nodeKey: NodeKey = TEMPLATE_KEY;
    if (contextDatasetId !== undefined) {
      nodeKey = `dataset-${contextDatasetId}`;
    } else if (contextNodeId !== undefined) {
      nodeKey = `category-${contextNodeId}`;
    }

    return computeStateForNode(variableName, nodeKey, contextNodeId, contextDatasetId);
  }, [hierarchyInfo, computeStateForNode]);

  const getInheritedValueForContext = useCallback((variableName: string, contextNodeId?: number, contextDatasetId?: number) => {
    if (!hierarchyInfo) return undefined;

    const {
      datasetInfos,
      datasetApplicableLevelsByVariable,
      variableDefinitionsByNode,
      templateVariableMap,
      variableMetaByName,
      categoryAncestorMap,
      nodeLevels
    } = hierarchyInfo;

    const extractValue = (variable?: VariableWithContext | null) => {
      if (!variable) return undefined;
      const value = variable.value !== undefined ? variable.value : variable.data;
      return value !== undefined ? value : undefined;
    };

    if (contextDatasetId !== undefined) {
      const datasetInfo = datasetInfos.get(contextDatasetId);
      if (!datasetInfo) {
        return undefined;
      }

      const levelMap = datasetApplicableLevelsByVariable.get(variableName);
      const allowedLevels = new Set(levelMap?.get(contextDatasetId) ?? []);

      for (let index = datasetInfo.categoryPath.length - 1; index >= 0; index--) {
        const category = datasetInfo.categoryPath[index];
        if (allowedLevels.size > 0 && !allowedLevels.has(category.level)) {
          continue;
        }
        const categoryVariable = variableDefinitionsByNode.get(category.key)?.get(variableName);
        if (isValueProvided(categoryVariable)) {
          return extractValue(categoryVariable);
        }
      }

      if (allowedLevels.size === 0 || allowedLevels.has(0)) {
        const templateVariable = templateVariableMap.get(variableName);
        if (isValueProvided(templateVariable)) {
          return extractValue(templateVariable);
        }
      }

      return undefined;
    }

    if (contextNodeId !== undefined) {
      const nodeKey: NodeKey = `category-${contextNodeId}`;
      const currentLevel = nodeLevels.get(nodeKey);
      if (currentLevel === undefined) {
        return undefined;
      }

      const meta = variableMetaByName.get(variableName) ?? { required: false, constraints: { minLevel: 0, maxLevel: 999 } };
      const ancestors = categoryAncestorMap.get(contextNodeId) ?? [];

      for (let index = ancestors.length - 1; index >= 0; index--) {
        const ancestorId = ancestors[index];
        const ancestorKey: NodeKey = `category-${ancestorId}`;
        const ancestorLevel = nodeLevels.get(ancestorKey);
        if (ancestorLevel === undefined) {
          continue;
        }
        if (ancestorLevel < meta.constraints.minLevel || ancestorLevel > meta.constraints.maxLevel) {
          continue;
        }
        const ancestorVariable = variableDefinitionsByNode.get(ancestorKey)?.get(variableName);
        if (isValueProvided(ancestorVariable)) {
          return extractValue(ancestorVariable);
        }
      }

      if (meta.constraints.minLevel <= 0 && meta.constraints.maxLevel >= 0) {
        const templateVariable = templateVariableMap.get(variableName);
        if (isValueProvided(templateVariable)) {
          return extractValue(templateVariable);
        }
      }

      return undefined;
    }

    return undefined;
  }, [hierarchyInfo]);

  const focusVariable = useCallback((variableName: string) => {
    setFocusedVariable(variableName);
  }, []);

  const unfocusVariable = useCallback(() => {
    setFocusedVariable(null);
  }, []);

  const isVariableFocused = useCallback((variableName: string) => {
    return focusedVariable === variableName;
  }, [focusedVariable]);

  const isDatasetReady = useCallback(
    (datasetId: number) => {
      if (!hierarchyInfo) return true;
      return hierarchyInfo.datasetReadyMap.get(datasetId) ?? true;
    },
    [hierarchyInfo]
  );

  const getNodeMissingSummary = useCallback(
    (nodeKey: string): MissingNodeSummary => {
      if (!hierarchyInfo) {
        return {
          missingDatasets: 0,
          totalDatasets: 0,
          details: []
        };
      }

      const counts = hierarchyInfo.nodeMissingDatasetCounts.get(nodeKey) ?? {
        missing: 0,
        total: 0
      };
      const details = hierarchyInfo.aggregatedMissingDetailsByNode.get(nodeKey) ?? [];

      return {
        missingDatasets: counts.missing,
        totalDatasets: counts.total,
        details
      };
    },
    [hierarchyInfo]
  );

  return {
    data,
    loading,
    error,
    refetch: fetchVariableTree,
    getVariablesByName,
    getVariableState,
    getVariableStateForContext,
    getInheritedValueForContext,
    focusedVariable,
    focusVariable,
    unfocusVariable,
    isVariableFocused,
    isDatasetReady,
    getNodeMissingSummary
  };
}
