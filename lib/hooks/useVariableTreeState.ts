import { useState, useCallback, useMemo } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { VariableWithContext } from '@/types/variable';
import { VariableChangeTracker, groupChangesByContext } from '@/lib/utils/changeTracking';
import { api } from '@/lib/services/api';
import { determineVariableSaveContext } from '@/lib/utils/variableState';
import { areValuesEqual } from '@/lib/utils/valueComparison';

export interface UseVariableTreeStateOptions {
  templateId: number;
  onSaveSuccess?: () => void;
  onSaveError?: (error: string) => void;
}

export interface UseVariableTreeStateReturn {
  // State management
  variableChanges: Map<string, any>;
  hasChanges: boolean;

  // Actions
  updateVariable: (
    variableName: string,
    newValue: any,
    originalVariable: VariableWithContext,
    nodeId?: number,
    nodeType?: 'category' | 'dataset',
    options?: { force?: boolean }
  ) => void;
  resetVariable: (variableName: string, nodeId?: number, nodeType?: 'category' | 'dataset', originalVariable?: VariableWithContext) => void;
  resetAllChanges: () => void;
  saveChanges: () => Promise<void>;
  dryRunSave: () => Promise<{
    groups: Array<{
      contextKey: string;
      saveLevel: 'template' | 'category' | 'dataset' | string;
      targetId?: number;
      setResponse?: any;
      resetResponses?: Array<{ variableName: string; response: any }>;
    }>;
  }>;

  // Reset to inherited functionality
  resetToInherited: (variableName: string, nodeId?: number, nodeType?: 'category' | 'dataset', inheritedValue?: any) => Promise<void>;

  // Status
  isSaving: boolean;
  saveError: string | null;
  isResetting: boolean;
  resetError: string | null;

  // Helpers
  getVariableValue: (variableName: string, originalValue: any, nodeId?: number, nodeType?: 'category' | 'dataset') => any;
  isVariableChanged: (variableName: string, nodeId?: number, nodeType?: 'category' | 'dataset') => boolean;
  isVariableResetPending: (variableName: string, nodeId?: number, nodeType?: 'category' | 'dataset') => boolean;

  // Debug
  debugInfo: () => {
    variableChangesObject: Record<string, any>;
    changeList: import('@/types/variable').VariableChange[];
    groupedByContext: Array<{
      contextKey: string;
      saveLevel: string;
      targetId?: number | undefined;
      changes: import('@/types/variable').VariableChange[];
    }>;
    savePlan: Array<{
      contextKey: string;
      saveLevel: 'template' | 'category' | 'dataset' | string;
      targetId?: number;
      set: { variableOverrides: Record<string, any> } | null;
      reset: string[];
    }>;
  };
}

/**
 * Hook for managing Variable Tree state changes and saving
 */
export function useVariableTreeState({
  templateId,
  onSaveSuccess,
  onSaveError
}: UseVariableTreeStateOptions): UseVariableTreeStateReturn {
  const [variableChanges, setVariableChanges] = useState<Map<string, any>>(new Map());
  const [changeTracker] = useState(() => new VariableChangeTracker());
  const queryClient = useQueryClient();

  // React Query mutations for each save context
  const updateTemplateMutation = useMutation({
    mutationFn: ({ templateId, variableOverrides }: { templateId: number; variableOverrides: { [key: string]: any } }) =>
      api.updateTemplateVariables(templateId, variableOverrides),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variable-tree', templateId] });
    }
  });

  const updateCategoryMutation = useMutation({
    mutationFn: ({ categoryId, variableOverrides }: { categoryId: number; variableOverrides: { [key: string]: any } }) =>
      api.updateCategoryVariables(categoryId, variableOverrides),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variable-tree', templateId] });
    }
  });

  const updateDatasetMutation = useMutation({
    mutationFn: ({ datasetId, variableOverrides }: { datasetId: number; variableOverrides: { [key: string]: any } }) =>
      api.updateDatasetVariables(datasetId, variableOverrides),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variable-tree', templateId] });
    }
  });

  // Reset mutations for removing variable overrides
  const resetTemplateMutation = useMutation({
    mutationFn: ({ templateId, variableName }: { templateId: number; variableName: string }) =>
      api.resetTemplateVariable(templateId, variableName),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variable-tree', templateId] });
    }
  });

  const resetCategoryMutation = useMutation({
    mutationFn: ({ categoryId, variableName }: { categoryId: number; variableName: string }) =>
      api.resetCategoryVariable(categoryId, variableName),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variable-tree', templateId] });
    }
  });

  const resetDatasetMutation = useMutation({
    mutationFn: ({ datasetId, variableName }: { datasetId: number; variableName: string }) =>
      api.resetDatasetVariable(datasetId, variableName),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variable-tree', templateId] });
    }
  });

  // Compute overall saving state and error from mutations
  const isSaving = updateTemplateMutation.isPending || updateCategoryMutation.isPending || updateDatasetMutation.isPending;
  const saveError = updateTemplateMutation.error?.message || updateCategoryMutation.error?.message || updateDatasetMutation.error?.message || null;

  // Compute overall resetting state and error from reset mutations
  const isResetting = resetTemplateMutation.isPending || resetCategoryMutation.isPending || resetDatasetMutation.isPending;
  const resetError = resetTemplateMutation.error?.message || resetCategoryMutation.error?.message || resetDatasetMutation.error?.message || null;

  // Update a variable value
  const updateVariable = useCallback((
    variableName: string,
    newValue: any,
    originalVariable: VariableWithContext,
    nodeId?: number,
    nodeType?: 'category' | 'dataset',
    options?: { force?: boolean }
  ) => {
    const saveContext = determineVariableSaveContext(originalVariable, nodeId, nodeType);
    const key = VariableChangeTracker.buildChangeKey(variableName, saveContext);
    const baseValue = originalVariable?.value ?? originalVariable?.data;

    if (!options?.force && areValuesEqual(baseValue, newValue)) {
      setVariableChanges(prev => {
        if (!prev.has(key)) {
          return prev;
        }
        const next = new Map(prev);
        next.delete(key);
        return next;
      });
      changeTracker.removeChange(variableName, saveContext);
      return;
    }

    setVariableChanges(prev => {
      const next = new Map(prev);
      next.set(key, newValue);
      return next;
    });

    changeTracker.addChange(
      variableName,
      baseValue,
      newValue,
      originalVariable,
      nodeId,
      nodeType
    );

    // Clear any previous save error (mutations handle their own error state)
  }, [changeTracker]);

  // Reset a single variable
  const resetVariable = useCallback((variableName: string, nodeId?: number, nodeType?: 'category' | 'dataset', originalVariable?: VariableWithContext) => {
    // Build the same composite key used when updating
    const saveContext = originalVariable
      ? determineVariableSaveContext(originalVariable, nodeId, nodeType)
      : ({ saveLevel: (nodeType ?? 'template'), targetId: nodeId } as const);
    const key = VariableChangeTracker.buildChangeKey(variableName, saveContext);

    setVariableChanges(prev => {
      const newChanges = new Map(prev);
      newChanges.delete(key);
      return newChanges;
    });

    changeTracker.removeChange(variableName, saveContext);
  }, [changeTracker]);

  // Reset all changes
  const resetAllChanges = useCallback(() => {
    setVariableChanges(new Map());
    changeTracker.clearChanges();
    // Reset mutation states
    updateTemplateMutation.reset();
    updateCategoryMutation.reset();
    updateDatasetMutation.reset();
  }, [changeTracker, updateTemplateMutation, updateCategoryMutation, updateDatasetMutation]);

  // Get the current value for a variable (changed or original)
  const getVariableValue = useCallback((variableName: string, originalValue: any, nodeId?: number, nodeType?: 'category' | 'dataset') => {
    const saveContext = { saveLevel: nodeType ?? 'template', targetId: nodeId } as const;
    const key = VariableChangeTracker.buildChangeKey(variableName, saveContext);
    return variableChanges.has(key) ? variableChanges.get(key) : originalValue;
  }, [variableChanges]);

  // Check if a variable has been changed
  const isVariableChanged = useCallback((variableName: string, nodeId?: number, nodeType?: 'category' | 'dataset') => {
    const saveContext = { saveLevel: nodeType ?? 'template', targetId: nodeId } as const;
    const key = VariableChangeTracker.buildChangeKey(variableName, saveContext);
    return variableChanges.has(key);
  }, [variableChanges]);

  // Check if there are any changes (includes pending resets tracked in changeTracker)
  const hasChanges = useMemo(() => {
    return variableChanges.size > 0 || changeTracker.hasChanges();
  }, [variableChanges, changeTracker]);

  // Track whether a variable has a pending reset in the change tracker
  const isVariableResetPending = useCallback((variableName: string, nodeId?: number, nodeType?: 'category' | 'dataset') => {
    return changeTracker
      .getChanges()
      .some(c => c.variableName === variableName
        && c.action === 'reset'
        && ((nodeType ?? 'template') === c.saveContext.saveLevel)
        && ((nodeType ? nodeId : undefined) === c.saveContext.targetId)
      );
  }, [changeTracker]);

  // Build a deterministic preview of what would be sent on save
  const buildSavePlan = useCallback(() => {
    const changes = changeTracker.getChanges();
    const changesByContext = groupChangesByContext(changes);

    const plan: Array<{
      contextKey: string;
      saveLevel: 'template' | 'category' | 'dataset' | string;
      targetId?: number;
      set: { variableOverrides: Record<string, any> } | null;
      reset: string[];
    }> = [];

    changesByContext.forEach((contextChanges, contextKey) => {
      const first = contextChanges[0];
      let saveLevel: 'template' | 'category' | 'dataset' | string = first?.saveContext?.saveLevel ?? (contextKey === 'template' ? 'template' : 'unknown');
      let targetId: number | undefined = first?.saveContext?.targetId;

      const setChanges = contextChanges.filter(c => c.action !== 'reset');
      const resetChanges = contextChanges.filter(c => c.action === 'reset');

      const set = setChanges.length > 0
        ? {
            variableOverrides: setChanges.reduce<Record<string, any>>((acc, c) => {
              acc[c.variableName] = c.newValue;
              return acc;
            }, {})
          }
        : null;

      plan.push({
        contextKey,
        saveLevel,
        targetId,
        set,
        reset: resetChanges.map(c => c.variableName)
      });
    });

    return plan;
  }, [changeTracker]);

  // Dry run save: calls the same endpoints with ?dry_run=true and returns per-group responses
  const dryRunSave = useCallback(async () => {
    const changes = changeTracker.getChanges();
    const changesByContext = groupChangesByContext(changes);
    const results: Array<{
      contextKey: string;
      saveLevel: 'template' | 'category' | 'dataset' | string;
      targetId?: number;
      setResponse?: any;
      resetResponses?: Array<{ variableName: string; response: any }>;
    }> = [];

    for (const [contextKey, contextChanges] of Array.from(changesByContext.entries())) {
      const first = contextChanges[0];
      const saveLevel = first?.saveContext?.saveLevel ?? (contextKey === 'template' ? 'template' : 'unknown');
      const targetId = first?.saveContext?.targetId;
      const setChanges = contextChanges.filter(c => c.action !== 'reset');
      const resetChanges = contextChanges.filter(c => c.action === 'reset');

      const entry: any = { contextKey, saveLevel, targetId, resetResponses: [] };

      if (setChanges.length > 0) {
        const variableOverrides: Record<string, any> = {};
        for (const c of setChanges) variableOverrides[c.variableName] = c.newValue;

        if (saveLevel === 'template') {
          entry.setResponse = await api.updateTemplateVariables(templateId, variableOverrides, { dryRun: true });
        } else if (saveLevel === 'category' && targetId != null) {
          entry.setResponse = await api.updateCategoryVariables(targetId, variableOverrides, { dryRun: true });
        } else if (saveLevel === 'dataset' && targetId != null) {
          entry.setResponse = await api.updateDatasetVariables(targetId, variableOverrides, { dryRun: true });
        } else {
          console.warn('⚠️ Skipping dry-run set with invalid context', { contextKey, saveLevel, targetId, variableOverrides });
        }
      }

      for (const rc of resetChanges) {
        if (saveLevel === 'template') {
          const resp = await api.resetTemplateVariable(templateId, rc.variableName, { dryRun: true });
          entry.resetResponses.push({ variableName: rc.variableName, response: resp });
        } else if (saveLevel === 'category' && targetId != null) {
          const resp = await api.resetCategoryVariable(targetId, rc.variableName, { dryRun: true });
          entry.resetResponses.push({ variableName: rc.variableName, response: resp });
        } else if (saveLevel === 'dataset' && targetId != null) {
          const resp = await api.resetDatasetVariable(targetId, rc.variableName, { dryRun: true });
          entry.resetResponses.push({ variableName: rc.variableName, response: resp });
        } else {
          console.warn('⚠️ Skipping dry-run reset with invalid context', { contextKey, saveLevel, targetId, variableName: rc.variableName });
        }
      }

      results.push(entry);
    }

    return { groups: results };
  }, [changeTracker, templateId]);


  // Save all changes using react-query mutations
  const saveChanges = useCallback(async () => {
    // Check directly from changeTracker to avoid stale memo issues
    // when updateVariable and saveChanges are called in quick succession
    if (!changeTracker.hasChanges()) return;

    try {
      const changes = changeTracker.getChanges();

      // Group changes by save context
      const changesByContext = groupChangesByContext(changes);

      // Save each group using appropriate mutation
      const savePromises: Promise<any>[] = [];

      changesByContext.forEach((contextChanges, contextKey) => {
        // Derive save context from the first change in the group to avoid string parsing pitfalls
        const first = contextChanges[0];
        const saveLevel = first?.saveContext?.saveLevel ?? (contextKey === 'template' ? 'template' : 'unknown');
        const targetId = first?.saveContext?.targetId !== undefined ? String(first.saveContext.targetId) : undefined;

        // Separate set vs reset actions
        const setChanges = contextChanges.filter(c => c.action !== 'reset');
        const resetChanges = contextChanges.filter(c => c.action === 'reset');

        // 1) Apply set/override updates in bulk
        if (setChanges.length > 0) {
          const variableOverrides: { [key: string]: any } = {};
          for (const change of setChanges) {
            variableOverrides[change.variableName] = change.newValue;
          }

          if (saveLevel === 'template') {
            console.log('🔄 Saving to TEMPLATE level (set):', { templateId, variableOverrides });
            savePromises.push(updateTemplateMutation.mutateAsync({ templateId, variableOverrides }));
          } else if (saveLevel === 'category' && targetId && targetId !== 'template') {
            console.log('🔄 Saving to CATEGORY level (set):', { categoryId: parseInt(targetId), variableOverrides });
            savePromises.push(updateCategoryMutation.mutateAsync({ categoryId: parseInt(targetId), variableOverrides }));
          } else if (saveLevel === 'dataset' && targetId && targetId !== 'template') {
            console.log('🔄 Saving to DATASET level (set):', { datasetId: parseInt(targetId), variableOverrides });
            savePromises.push(updateDatasetMutation.mutateAsync({ datasetId: parseInt(targetId), variableOverrides }));
          } else {
            console.warn('⚠️ Skipping set group with invalid context', { contextKey, saveLevel, targetId, variableOverrides });
          }
        }

        // 2) Apply resets individually (API requires variableName)
        for (const resetChange of resetChanges) {
          if (saveLevel === 'template') {
            console.log('🗑️ Reset at TEMPLATE level:', { templateId, variableName: resetChange.variableName });
            savePromises.push(resetTemplateMutation.mutateAsync({ templateId, variableName: resetChange.variableName }));
          } else if (saveLevel === 'category' && targetId) {
            console.log('🗑️ Reset at CATEGORY level:', { categoryId: parseInt(targetId), variableName: resetChange.variableName });
            savePromises.push(resetCategoryMutation.mutateAsync({ categoryId: parseInt(targetId), variableName: resetChange.variableName }));
          } else if (saveLevel === 'dataset' && targetId) {
            console.log('🗑️ Reset at DATASET level:', { datasetId: parseInt(targetId), variableName: resetChange.variableName });
            savePromises.push(resetDatasetMutation.mutateAsync({ datasetId: parseInt(targetId), variableName: resetChange.variableName }));
          } else {
            console.warn('⚠️ Skipping reset with invalid context', { contextKey, saveLevel, targetId, variableName: resetChange.variableName });
          }
        }
      });

      // Wait for all mutations to complete
      await Promise.all(savePromises);

      // Clear changes after successful save
      resetAllChanges();
      onSaveSuccess?.();

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save changes';
      onSaveError?.(errorMessage);
    }
  }, [changeTracker, templateId, resetAllChanges, onSaveSuccess, onSaveError, updateTemplateMutation, updateCategoryMutation, updateDatasetMutation, resetTemplateMutation, resetCategoryMutation, resetDatasetMutation]);

  // Reset to inherited (LOCAL ONLY). Mark a pending reset and restore inherited value locally.
  const resetToInherited = useCallback(async (variableName: string, nodeId?: number, nodeType?: 'category' | 'dataset', inheritedValue?: any) => {
    try {
      // Determine context and composite key
      const existing = changeTracker.getChanges().find(c => c.variableName === variableName
        && ((nodeType ?? 'template') === c.saveContext.saveLevel)
        && ((nodeType ? nodeId : undefined) === c.saveContext.targetId)
      );
      const effectiveContext = existing?.saveContext ?? ({ saveLevel: (nodeType ?? 'template'), targetId: nodeId } as const);
      const key = VariableChangeTracker.buildChangeKey(variableName, effectiveContext);

      // Update local value to inherited for UI
      setVariableChanges(prev => {
        const next = new Map(prev);
        if (inheritedValue !== undefined) {
          next.set(key, inheritedValue);
        } else {
          next.delete(key);
        }
        return next;
      });

      // Use original variable from existing change if present
      const originalVariable = existing?.originalVariable as VariableWithContext | undefined;

      // Add pending reset action to the tracker
      changeTracker.addChange(
        variableName,
        originalVariable?.value,
        undefined,
        originalVariable as any,
        nodeId,
        nodeType,
        'reset'
      );
    } catch (error) {
      console.error('Failed to mark variable as reset locally:', error);
      throw error;
    }
  }, [changeTracker, setVariableChanges]);

  return {
    variableChanges,
    hasChanges,
    updateVariable,
    resetVariable,
    resetAllChanges,
    saveChanges,
    dryRunSave,
    resetToInherited,
    isSaving,
    saveError,
    isResetting,
    resetError,
    getVariableValue,
    isVariableChanged,
    isVariableResetPending,
    debugInfo: () => ({
      variableChangesObject: Array.from(variableChanges.entries()).reduce<Record<string, any>>((acc, [k, v]) => {
        acc[k] = v;
        return acc;
      }, {}),
      changeList: changeTracker.getChanges(),
      groupedByContext: Array.from(groupChangesByContext(changeTracker.getChanges()).entries()).map(([key, changes]) => {
        const [level, id] = key === 'template' ? ['template', undefined] : key.split('-');
        return {
          contextKey: key,
          saveLevel: level,
          targetId: id ? (Number.isFinite(parseInt(id, 10)) ? parseInt(id, 10) : undefined) : undefined,
          changes
        };
      }),
      savePlan: buildSavePlan(),
    })
  };
}
